@echo off
echo ============================================================
echo 🚀 启动视频处理程序（GPU优化模式）
echo 使用Python 3.8虚拟环境 + CUDA 12.9 + RTX 3060优化
echo ============================================================

REM 检查GPU状态
echo 🔍 检查GPU状态...
nvidia-smi --query-gpu=name,memory.used,memory.total,utilization.gpu --format=csv,noheader,nounits 2>nul
if %errorlevel% neq 0 (
    echo ❌ 警告：无法检测到NVIDIA GPU，将使用CPU模式
    pause
    exit /b 1
)

REM 设置GPU兼容性环境变量（针对RTX 3060优化）
echo 🔧 设置GPU兼容性环境变量...
set TF_FORCE_GPU_ALLOW_GROWTH=true
set TF_GPU_ALLOCATOR=cuda_malloc_async
set CUDA_MODULE_LOADING=LAZY
set TF_CUDA_COMPUTE_CAPABILITIES=8.6
set TF_GPU_THREAD_MODE=gpu_private
set TF_GPU_THREAD_COUNT=2
set TF_CPP_MIN_LOG_LEVEL=1
set TF_ENABLE_ONEDNN_OPTS=0
set TF_DISABLE_MKL=1
set CUDA_CACHE_DISABLE=0
set TF_ENABLE_GPU_GARBAGE_COLLECTION=true

REM 确保不强制使用CPU
set CUDA_VISIBLE_DEVICES=

REM FFmpeg GPU加速设置
set FFMPEG_HWACCEL=cuda

echo ✅ GPU兼容性环境变量设置完成
echo 🎯 目标GPU: NVIDIA GeForce RTX 3060
echo 🔧 CUDA版本: 12.9
echo 🧠 TensorFlow: 2.8.4 (虚拟环境)
echo.
echo 🚀 启动程序...

REM 使用虚拟环境中的Python
venv38\Scripts\python main.py

echo.
echo ✅ 程序执行完成
pause
