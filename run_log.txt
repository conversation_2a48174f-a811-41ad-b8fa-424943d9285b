[22:56:00] [伪原创] 全部7个阶段已完成！
Step 1/7 轻量视频扰动参数：
裁剪模式=no_crop
裁剪=左0px,右0px,上0px
亮度扰动=brightness:0.016
对比度扰动=contrast:1.01
镜像=否
旋转=+0.00°
色相扰动=hue:+1.61
饱和度扰动=saturation:+1.01
模糊sigma=0.47
缩放=1.023
平移=(1,5)
帧率扰动=29.73
边框厚度=4
锐化=1.5
噪点=轻微(强度=8)
滤镜链=noise=alls=8:allf=t,eq=brightness=0.016:contrast=1.01,hue=h=1.61:s=1.01,gblur=sigma=0.47,scale=iw*1.023:ih*1.023,crop=iw:ih:1:5,fps=29.73,drawbox=0:0:iw:ih:0xd6decc@0.14:t=4,unsharp=5:5:1.5:5:5:0.0,scale=1920:1080,setsar=1

Step 2/7 动态滤镜链参数：
动态效果数量=3
动态色相扰动=有 (幅度=0.021, 周期=12.2s)
动态旋转=无
动态亮度/对比度渐变=有 (幅度=0.024, 周期=14.8s)
插帧=有 (60fps)
反色=无
水印=有
水印图片: 已设置
水印位置: 将在Step 3中设置
水印幅度: 将在Step 3中设置
水印边距: 将在Step 3中设置
水印移动范围: 将在Step 3中设置px
水印移动速度: 将在Step 3中设置s/周期
反色混合透明度: 无
滤镜链: hue=h=0.3980827253352056,eq=brightness=0.014725911975661077,minterpolate=fps=60:mi_mode=blend,scale=1920:1080,setsar=1

Step 3/7 水印处理参数：
水印=有
水印图片: 已设置
水印位置: 将在Step 3中设置
水印幅度: 将在Step 3中设置
水印边距: 将在Step 3中设置
水印移动范围: (6,6)px
水印移动速度: 113.90s/周期

Step 4/7 音频扰动参数：
变速倍数=1.02
音高微调=0.47
白噪声强度=0.014
混音方式=amix
音频采样率=44100

Step 5/7 视频增强参数：
RIFE视频补帧=未应用
空间变形=已应用(cx=0.4903, cy=0.5025, k1=-0.0100, k2=0.0159)
超分辨率重建=已应用(1.5倍)
HDR转换=已应用
时间超分辨率=已应用

Step 6/7 视频加速/横屏转竖屏参数：
竖屏分辨率=1080x1920
背景色=0xeeeae4
pad滤镜=scale=1080:-1,pad=1080:1920:(ow-iw)/2:(oh-ih)/2:0xeeeae4,drawtext=fontfile=SourceHanSansSC-Medium.otf:text='一口气看完三皇五帝':fontsize=56:fontcolor=0x3e902a:x=(w-text_w)/2+2:y=500+2,drawtext=fontfile=SourceHanSansSC-Medium.otf:text='一口气看完三皇五帝':fontsize=56:fontcolor=0x68f147:x='(w-text_w)/2+8*sin(2*PI*t/101.62269892889572)':y='500+4.0*sin(2*PI*t/101.62269892889572+PI/4)'
文案内容=一口气看完三皇五帝

特效处理参数：
特效数量=0
粒子效果=无 (强度=0.00)
光效=无 (强度=0.00)
转场效果=无 (无)
文字边框=无 (宽度=0px)
高级特效数量=0

动态装饰参数：
动态边框=无 (颜色=0xd6decc@0.14, 宽度=0px, 速度=0.0)
装饰元素=0个
背景纹理=无 (强度=0.00)

[23:16:01] [伪原创] 全部7个阶段已完成！
Step 1/7 轻量视频扰动参数：
裁剪模式=no_crop
裁剪=左0px,右0px,上0px
亮度扰动=brightness:0.013
对比度扰动=contrast:1.02
镜像=否
旋转=+0.00°
色相扰动=hue:-0.73
饱和度扰动=saturation:+0.95
模糊sigma=0.31
缩放=1.012
平移=(-8,2)
帧率扰动=30.19
边框厚度=2
锐化=1.14
噪点=轻微(强度=8)
滤镜链=noise=alls=8:allf=t,eq=brightness=0.013:contrast=1.02,hue=h=-0.73:s=0.95,gblur=sigma=0.31,scale=iw*1.012:ih*1.012,crop=iw:ih:-8:2,fps=30.19,drawbox=0:0:iw:ih:0xede9e4@0.15:t=2,unsharp=5:5:1.14:5:5:0.0,scale=1920:1080,setsar=1

Step 2/7 动态滤镜链参数：
动态效果数量=1
动态色相扰动=无
动态旋转=无
动态亮度/对比度渐变=无
插帧=无
反色=无
水印=有
水印图片: 已设置
水印位置: 将在Step 3中设置
水印幅度: 将在Step 3中设置
水印边距: 将在Step 3中设置
水印移动范围: 将在Step 3中设置px
水印移动速度: 将在Step 3中设置s/周期
反色混合透明度: 无
滤镜链: gblur=sigma=0.9207541656155738,scale=1920:1080,setsar=1

Step 3/7 水印处理参数：
水印=有
水印图片: 已设置
水印位置: 将在Step 3中设置
水印幅度: 将在Step 3中设置
水印边距: 将在Step 3中设置
水印移动范围: (6,8)px
水印移动速度: 95.35s/周期

Step 4/7 音频扰动参数：
变速倍数=1.01
音高微调=0.44
白噪声强度=0.008
混音方式=amix
音频采样率=44100

Step 5/7 视频增强参数：
RIFE视频补帧=未应用
空间变形=已应用(cx=0.4928, cy=0.4845, k1=0.0085, k2=0.0059)
超分辨率重建=已应用(1.5倍)
HDR转换=已应用
时间超分辨率=已应用

Step 6/7 视频加速/横屏转竖屏参数：
竖屏分辨率=1080x1920
背景色=0xd3ddc1
pad滤镜=scale=1080:-1,pad=1080:1920:(ow-iw)/2:(oh-ih)/2:0xd3ddc1,drawtext=fontfile=SourceHanSansSC-Medium.otf:text='历史上的今天':fontsize=56:fontcolor=0xd385f7:x=(w-text_w)/2:y=500
文案内容=历史上的今天

特效处理参数：
特效数量=0
粒子效果=无 (强度=0.00)
光效=无 (强度=0.00)
转场效果=无 (无)
文字边框=无 (宽度=0px)
高级特效数量=0

动态装饰参数：
动态边框=无 (颜色=0xede9e4@0.15, 宽度=0px, 速度=0.0)
装饰元素=0个
背景纹理=无 (强度=0.00)

[00:35:38] [伪原创] 全部7个阶段已完成！
Step 1/7 轻量视频扰动参数：
裁剪模式=no_crop
裁剪=左0px,右0px,上0px
亮度扰动=brightness:0.019
对比度扰动=contrast:1.03
镜像=否
旋转=+0.00°
色相扰动=hue:+0.87
饱和度扰动=saturation:+1.05
模糊sigma=0.4
缩放=1.014
平移=(8,0)
帧率扰动=29.83
边框厚度=5
锐化=1.3
噪点=轻微(强度=4)
滤镜链=noise=alls=4:allf=t,eq=brightness=0.019:contrast=1.03,hue=h=0.87:s=1.05,gblur=sigma=0.4,scale=iw*1.014:ih*1.014,crop=iw:ih:8:0,fps=29.83,drawbox=0:0:iw:ih:0xe0d4e6@0.17:t=5,unsharp=5:5:1.3:5:5:0.0,scale=1920:1080,setsar=1

Step 2/7 动态滤镜链参数：
动态效果数量=4
动态色相扰动=有 (幅度=0.034, 周期=9.4s)
动态旋转=无
动态亮度/对比度渐变=有 (幅度=0.019, 周期=15.4s)
插帧=无
反色=有
水印=有
水印图片: 已设置
水印位置: 将在Step 3中设置
水印幅度: 将在Step 3中设置
水印边距: 将在Step 3中设置
水印移动范围: 将在Step 3中设置px
水印移动速度: 将在Step 3中设置s/周期
反色混合透明度: 0.159
滤镜链: hue=h=-0.48005683131792243,eq=brightness=0.014851074275601544,scale=iw*1.013775013497862:ih*1.013775013497862,split[a][b];[b]negate[b];[a][b]blend=all_opacity=0.15862147524887607,scale=1920:1080,setsar=1

Step 3/7 水印处理参数：
水印=有
水印图片: 已设置
水印位置: 将在Step 3中设置
水印幅度: 将在Step 3中设置
水印边距: 将在Step 3中设置
水印移动范围: (10,7)px
水印移动速度: 113.64s/周期

Step 4/7 音频扰动参数：
变速倍数=0.97
音高微调=-0.43
白噪声强度=0.008
混音方式=amix
音频采样率=44100

Step 5/7 视频增强参数：
RIFE视频补帧=未应用
空间变形=已应用(cx=0.5176, cy=0.5078, k1=-0.0174, k2=-0.0024)
超分辨率重建=已应用(1.5倍)
HDR转换=已应用
时间超分辨率=已应用

Step 6/7 视频加速/横屏转竖屏参数：
竖屏分辨率=1080x1920
背景色=0xc1dddb
pad滤镜=scale=1080:-1,pad=1080:1920:(ow-iw)/2:(oh-ih)/2:0xc1dddb,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完上古时期的三皇五帝':fontsize=66:fontcolor=black:x=(w-text_w)/2+2:y=500+2,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完上古时期的三皇五帝':fontsize=66:fontcolor=black:x=(w-text_w)/2-2:y=500+2,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完上古时期的三皇五帝':fontsize=66:fontcolor=black:x=(w-text_w)/2+2:y=500-2,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完上古时期的三皇五帝':fontsize=66:fontcolor=black:x=(w-text_w)/2-2:y=500-2,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完上古时期的三皇五帝':fontsize=66:fontcolor=0xf5e586:x='(w-text_w)/2+5*sin(2*PI*t/105.8821197447572)':y='500+2.5*sin(2*PI*t/105.8821197447572+PI/4)'
文案内容=一口气看完上古时期的三皇五帝

特效处理参数：
特效数量=0
粒子效果=无 (强度=0.00)
光效=无 (强度=0.00)
转场效果=无 (无)
文字边框=无 (宽度=0px)
高级特效数量=0

动态装饰参数：
动态边框=无 (颜色=0xe0d4e6@0.17, 宽度=0px, 速度=0.0)
装饰元素=0个
背景纹理=无 (强度=0.00)

[01:19:04] [伪原创] 全部7个阶段已完成！
Step 1/7 轻量视频扰动参数：
裁剪模式=no_crop
裁剪=左0px,右0px,上0px
亮度扰动=brightness:0.017
对比度扰动=contrast:1.02
镜像=否
旋转=+0.00°
色相扰动=hue:-1.00
饱和度扰动=saturation:+1.02
模糊sigma=0.45
缩放=1.029
平移=(7,8)
帧率扰动=29.99
边框厚度=6
锐化=1.25
噪点=轻微(强度=7)
滤镜链=noise=alls=7:allf=t,eq=brightness=0.017:contrast=1.02,hue=h=-1.0:s=1.02,gblur=sigma=0.45,scale=iw*1.029:ih*1.029,crop=iw:ih:7:8,fps=29.99,drawbox=0:0:iw:ih:0xcbd0e4@0.16:t=6,unsharp=5:5:1.25:5:5:0.0,scale=1920:1080,setsar=1

Step 2/7 动态滤镜链参数：
动态效果数量=2
动态色相扰动=无
动态旋转=无
动态亮度/对比度渐变=有 (幅度=0.029, 周期=15.1s)
插帧=无
反色=有
水印=有
水印图片: 已设置
水印位置: 将在Step 3中设置
水印幅度: 将在Step 3中设置
水印边距: 将在Step 3中设置
水印移动范围: 将在Step 3中设置px
水印移动速度: 将在Step 3中设置s/周期
反色混合透明度: 0.119
滤镜链: eq=brightness=0.012606641312902112,split[a][b];[b]negate[b];[a][b]blend=all_opacity=0.11865800607754828,scale=1920:1080,setsar=1

Step 3/7 水印处理参数：
水印=有
水印图片: 已设置
水印位置: 将在Step 3中设置
水印幅度: 将在Step 3中设置
水印边距: 将在Step 3中设置
水印移动范围: (7,7)px
水印移动速度: 73.79s/周期

Step 4/7 音频扰动参数：
变速倍数=0.98
音高微调=0.23
白噪声强度=0.011
混音方式=amix
音频采样率=44100

Step 5/7 视频增强参数：
RIFE视频补帧=未应用
空间变形=已应用(cx=0.4997, cy=0.4863, k1=-0.0090, k2=0.0004)
超分辨率重建=已应用(1.5倍)
HDR转换=已应用
时间超分辨率=已应用

Step 6/7 视频加速/横屏转竖屏参数：
竖屏分辨率=1080x1920
背景色=0xece4ee
pad滤镜=scale=1080:-1,pad=1080:1920:(ow-iw)/2:(oh-ih)/2:0xece4ee,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完三皇五帝':fontsize=70:fontcolor=black:x=(w-text_w)/2+2:y=500+2,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完三皇五帝':fontsize=70:fontcolor=black:x=(w-text_w)/2-2:y=500+2,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完三皇五帝':fontsize=70:fontcolor=black:x=(w-text_w)/2+2:y=500-2,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完三皇五帝':fontsize=70:fontcolor=black:x=(w-text_w)/2-2:y=500-2,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完三皇五帝':fontsize=70:fontcolor=0xadf4fb:x='(w-text_w)/2+3*sin(2*PI*t/97.38040372706172)':y='500+1.5*sin(2*PI*t/97.38040372706172+PI/4)'
文案内容=一口气看完三皇五帝

特效处理参数：
特效数量=0
粒子效果=无 (强度=0.00)
光效=无 (强度=0.00)
转场效果=无 (无)
文字边框=无 (宽度=0px)
高级特效数量=0

动态装饰参数：
动态边框=无 (颜色=0xcbd0e4@0.16, 宽度=0px, 速度=0.0)
装饰元素=0个
背景纹理=无 (强度=0.00)

[01:28:49] [伪原创] 全部8个阶段已完成！
Step 1/8 轻量视频扰动参数：
裁剪模式=no_crop
裁剪=左0px,右0px,上0px
亮度扰动=brightness:0.010
对比度扰动=contrast:1.02
镜像=否
旋转=+0.00°
色相扰动=hue:+0.08
饱和度扰动=saturation:+0.98
模糊sigma=0.26
缩放=1.011
平移=(4,6)
帧率扰动=29.87
边框厚度=6
锐化=0.98
噪点=轻微(强度=6)
滤镜链=noise=alls=6:allf=t,eq=brightness=0.010:contrast=1.02,hue=h=0.08:s=0.98,gblur=sigma=0.26,scale=iw*1.011:ih*1.011,crop=iw:ih:4:6,fps=29.87,drawbox=0:0:iw:ih:0xe1dcec@0.13:t=6,unsharp=5:5:0.98:5:5:0.0,scale=1920:1080,setsar=1

Step 2/8 动态滤镜链参数：
动态效果数量=2
动态色相扰动=无
动态旋转=无
动态亮度/对比度渐变=有 (幅度=0.012, 周期=19.2s)
插帧=无
反色=无
水印=有
水印图片: 已设置
水印位置: 将在Step 3中设置
水印幅度: 将在Step 3中设置
水印边距: 将在Step 3中设置
水印移动范围: 将在Step 3中设置px
水印移动速度: 将在Step 3中设置s/周期
反色混合透明度: 无
滤镜链: eq=brightness=-0.006748076671224226,scale=iw*1.0283489396808323:ih*1.0283489396808323,scale=1920:1080,setsar=1

Step 3/8 水印处理参数：
水印=有
水印图片: 已设置
水印位置: 将在Step 3中设置
水印幅度: 将在Step 3中设置
水印边距: 将在Step 3中设置
水印移动范围: (5,3)px
水印移动速度: 85.00s/周期

Step 4/8 音频扰动参数：
变速倍数=0.98
音高微调=0.41
白噪声强度=0.013
混音方式=amix
音频采样率=44100

Step 5/8 视频增强参数：
RIFE视频补帧=未应用
空间变形=已应用(cx=0.5000, cy=0.4953, k1=0.0408, k2=-0.0051)
超分辨率重建=已应用(1.5倍)
HDR转换=已应用
时间超分辨率=已应用

Step 6/8 视频加速/横屏转竖屏参数：
竖屏分辨率=1080x1920
背景色=0xc9e3d8
pad滤镜=scale=1080:-1,pad=1080:1920:(ow-iw)/2:(oh-ih)/2:0xc9e3d8,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完三皇五帝':fontsize=72:fontcolor=black:x=(w-text_w)/2+2:y=500+2,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完三皇五帝':fontsize=72:fontcolor=black:x=(w-text_w)/2-2:y=500+2,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完三皇五帝':fontsize=72:fontcolor=black:x=(w-text_w)/2+2:y=500-2,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完三皇五帝':fontsize=72:fontcolor=black:x=(w-text_w)/2-2:y=500-2,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完三皇五帝':fontsize=72:fontcolor=0xfe9dd6:x='(w-text_w)/2+6*sin(2*PI*t/104.28395995018012)':y='500+3.0*sin(2*PI*t/104.28395995018012+PI/4)'
文案内容=一口气看完三皇五帝

特效处理参数：
特效数量=0
粒子效果=无 (强度=0.00)
光效=无 (强度=0.00)
转场效果=无 (无)
文字边框=无 (宽度=0px)
高级特效数量=0

动态装饰参数：
动态边框=无 (颜色=0xe1dcec@0.13, 宽度=0px, 速度=0.0)
装饰元素=0个
背景纹理=无 (强度=0.00)

[01:50:18] [伪原创] 全部8个阶段已完成！
Step 1/8 轻量视频扰动参数：
裁剪模式=no_crop
裁剪=左0px,右0px,上0px
亮度扰动=brightness:0.017
对比度扰动=contrast:1.03
镜像=否
旋转=+0.00°
色相扰动=hue:+0.71
饱和度扰动=saturation:+1.00
模糊sigma=0.28
缩放=1.023
平移=(4,8)
帧率扰动=29.85
边框厚度=4
锐化=1.08
噪点=轻微(强度=5)
滤镜链=noise=alls=5:allf=t,eq=brightness=0.017:contrast=1.03,hue=h=0.71:s=1.0,gblur=sigma=0.28,scale=iw*1.023:ih*1.023,crop=iw:ih:4:8,fps=29.85,drawbox=0:0:iw:ih:0xbbd2d9@0.13:t=4,unsharp=5:5:1.08:5:5:0.0,scale=1920:1080,setsar=1

Step 2/8 动态滤镜链参数：
动态效果数量=4
动态色相扰动=无
动态旋转=无
动态亮度/对比度渐变=有 (幅度=0.029, 周期=15.0s)
插帧=有 (60fps)
反色=有
水印=有
水印图片: 已设置
水印位置: 将在Step 3中设置
水印幅度: 将在Step 3中设置
水印边距: 将在Step 3中设置
水印移动范围: 将在Step 3中设置px
水印移动速度: 将在Step 3中设置s/周期
反色混合透明度: 0.028
滤镜链: eq=brightness=0.00015059300295051578,scale=iw*1.0245965333157636:ih*1.0245965333157636,fps=fps=60,split[a][b];[b]negate[b];[a][b]blend=all_opacity=0.02844927995664693,scale=1920:1080,setsar=1

Step 3/8 水印处理参数：
水印=有
水印图片: 已设置
水印位置: 将在Step 3中设置
水印幅度: 将在Step 3中设置
水印边距: 将在Step 3中设置
水印移动范围: (5,6)px
水印移动速度: 106.47s/周期

Step 4/8 音频扰动参数：
变速倍数=1.01
音高微调=0.09
白噪声强度=0.014
混音方式=amix
音频采样率=44100

Step 5/8 视频增强参数：
RIFE视频补帧=未应用
空间变形=已应用(cx=0.5065, cy=0.5040, k1=-0.0347, k2=-0.0183)
超分辨率重建=已应用(1.5倍)
HDR转换=已应用
时间超分辨率=已应用

Step 6/8 视频加速/横屏转竖屏参数：
竖屏分辨率=1080x1920
背景色=0xead8e7
pad滤镜=scale=1080:-1,pad=1080:1920:(ow-iw)/2:(oh-ih)/2:0xead8e7,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完上古时期的三皇五帝':fontsize=66:fontcolor=black:x=(w-text_w)/2+2:y=500+2,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完上古时期的三皇五帝':fontsize=66:fontcolor=black:x=(w-text_w)/2-2:y=500+2,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完上古时期的三皇五帝':fontsize=66:fontcolor=black:x=(w-text_w)/2+2:y=500-2,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完上古时期的三皇五帝':fontsize=66:fontcolor=black:x=(w-text_w)/2-2:y=500-2,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完上古时期的三皇五帝':fontsize=66:fontcolor=0xa3cffe:x='(w-text_w)/2+5*sin(2*PI*t/112.55264025521507)':y='500+2.5*sin(2*PI*t/112.55264025521507+PI/4)'
文案内容=一口气看完上古时期的三皇五帝

特效处理参数：
特效数量=0
粒子效果=无 (强度=0.00)
光效=无 (强度=0.00)
转场效果=无 (无)
文字边框=无 (宽度=0px)
高级特效数量=0

动态装饰参数：
动态边框=无 (颜色=0xbbd2d9@0.13, 宽度=0px, 速度=0.0)
装饰元素=0个
背景纹理=无 (强度=0.00)

[02:02:15] [伪原创] 全部8个阶段已完成！
Step 1/8 轻量视频扰动参数：
裁剪模式=no_crop
裁剪=左0px,右0px,上0px
亮度扰动=brightness:0.010
对比度扰动=contrast:1.02
镜像=否
旋转=+0.00°
色相扰动=hue:-0.46
饱和度扰动=saturation:+1.00
模糊sigma=0.45
缩放=1.024
平移=(5,2)
帧率扰动=30.06
边框厚度=2
锐化=0.91
噪点=轻微(强度=4)
滤镜链=noise=alls=4:allf=t,eq=brightness=0.010:contrast=1.02,hue=h=-0.46:s=1.0,gblur=sigma=0.45,scale=iw*1.024:ih*1.024,crop=iw:ih:5:2,fps=30.06,drawbox=0:0:iw:ih:0xd8cdb9@0.08:t=2,unsharp=5:5:0.91:5:5:0.0,scale=1920:1080,setsar=1

Step 2/8 动态滤镜链参数：
动态效果数量=2
动态色相扰动=有 (幅度=0.021, 周期=14.6s)
动态旋转=无
动态亮度/对比度渐变=有 (幅度=0.017, 周期=13.5s)
插帧=无
反色=无
水印=有
水印图片: 已设置
水印位置: 将在Step 3中设置
水印幅度: 将在Step 3中设置
水印边距: 将在Step 3中设置
水印移动范围: 将在Step 3中设置px
水印移动速度: 将在Step 3中设置s/周期
反色混合透明度: 无
滤镜链: hue=h=-0.21817934021338847,eq=brightness=0.011893534910650903,scale=1920:1080,setsar=1

Step 3/8 水印处理参数：
水印=有
水印图片: 已设置
水印位置: 将在Step 3中设置
水印幅度: 将在Step 3中设置
水印边距: 将在Step 3中设置
水印移动范围: (15,4)px
水印移动速度: 108.25s/周期

Step 4/8 音频扰动参数：
变速倍数=1.02
音高微调=0.39
白噪声强度=0.01
混音方式=amix
音频采样率=44100

Step 5/8 视频增强参数：
RIFE视频补帧=未应用
空间变形=已应用(cx=0.4956, cy=0.5041, k1=-0.0139, k2=0.0102)
超分辨率重建=已应用(1.5倍)
HDR转换=已应用
时间超分辨率=已应用

Step 6/8 视频加速/横屏转竖屏参数：
竖屏分辨率=1080x1920
背景色=0xdce3d4
pad滤镜=scale=1080:-1,pad=1080:1920:(ow-iw)/2:(oh-ih)/2:0xdce3d4,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完上古时期的三皇五帝':fontsize=66:fontcolor=black:x=(w-text_w)/2+2:y=500+2,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完上古时期的三皇五帝':fontsize=66:fontcolor=black:x=(w-text_w)/2-2:y=500+2,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完上古时期的三皇五帝':fontsize=66:fontcolor=black:x=(w-text_w)/2+2:y=500-2,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完上古时期的三皇五帝':fontsize=66:fontcolor=black:x=(w-text_w)/2-2:y=500-2,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完上古时期的三皇五帝':fontsize=66:fontcolor=0xf9f377:x='(w-text_w)/2+3*sin(2*PI*t/71.73397207335624)':y='500+1.5*sin(2*PI*t/71.73397207335624+PI/4)'
文案内容=一口气看完上古时期的三皇五帝

特效处理参数：
特效数量=0
粒子效果=无 (强度=0.00)
光效=无 (强度=0.00)
转场效果=无 (无)
文字边框=无 (宽度=0px)
高级特效数量=0

动态装饰参数：
动态边框=无 (颜色=0xd8cdb9@0.08, 宽度=0px, 速度=0.0)
装饰元素=0个
背景纹理=无 (强度=0.00)

[05:43:34] [伪原创] 全部8个阶段已完成！
Step 1/8 轻量视频扰动参数：
裁剪模式=no_crop
裁剪=左0px,右0px,上0px
亮度扰动=brightness:0.010
对比度扰动=contrast:1.01
镜像=否
旋转=+0.00°
色相扰动=hue:+0.88
饱和度扰动=saturation:+0.99
模糊sigma=0.6
缩放=1.024
平移=(8,8)
帧率扰动=29.86
边框厚度=3
锐化=1.44
噪点=轻微(强度=3)
滤镜链=noise=alls=3:allf=t,eq=brightness=0.010:contrast=1.01,hue=h=0.88:s=0.99,gblur=sigma=0.6,scale=iw*1.024:ih*1.024,crop=iw:ih:8:8,fps=29.86,drawbox=0:0:iw:ih:0xbdd9d9@0.13:t=3,unsharp=5:5:1.44:5:5:0.0,scale=1920:1080,setsar=1

Step 2/8 动态滤镜链参数：
动态效果数量=2
动态色相扰动=有 (幅度=0.050, 周期=11.6s)
动态旋转=无
动态亮度/对比度渐变=无
插帧=有 (60fps)
水印=有
水印图片: 已设置
水印位置: 将在Step 3中设置
水印幅度: 将在Step 3中设置
水印边距: 将在Step 3中设置
水印移动范围: 将在Step 3中设置px
水印移动速度: 将在Step 3中设置s/周期
滤镜链: hue=h=0.2113041825798797,fps=fps=31.198571,setpts=1.0*PTS,scale=1920:1080,setsar=1

Step 3/8 水印处理参数：
水印=有
水印图片: 已设置
水印位置: 将在Step 3中设置
水印幅度: 将在Step 3中设置
水印边距: 将在Step 3中设置
水印移动范围: (14,8)px
水印移动速度: 83.27s/周期

Step 4/8 视频增强参数：
RIFE视频补帧=未应用
空间变形=已应用(cx=0.4962, cy=0.5105, k1=-0.0233, k2=-0.0114)
超分辨率重建=已应用(1.5倍)
HDR转换=已应用
时间超分辨率=已应用

Step 5/8 视频加速/横屏转竖屏参数：
竖屏分辨率=1080x1920
背景色=0xcfd9e4
pad滤镜=scale=1080:-1,pad=1080:1920:(ow-iw)/2:(oh-ih)/2:0xcfd9e4,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完上古时期的三皇五帝':fontsize=71:fontcolor=black:x=(w-text_w)/2+5:y=500+5,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完上古时期的三皇五帝':fontsize=71:fontcolor=black:x=(w-text_w)/2-5:y=500+5,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完上古时期的三皇五帝':fontsize=71:fontcolor=black:x=(w-text_w)/2+5:y=500-5,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完上古时期的三皇五帝':fontsize=71:fontcolor=black:x=(w-text_w)/2-5:y=500-5,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完上古时期的三皇五帝':fontsize=71:fontcolor=white:x=(w-text_w)/2+2:y=500+2,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完上古时期的三皇五帝':fontsize=71:fontcolor=white:x=(w-text_w)/2-2:y=500+2,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完上古时期的三皇五帝':fontsize=71:fontcolor=white:x=(w-text_w)/2+2:y=500-2,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完上古时期的三皇五帝':fontsize=71:fontcolor=white:x=(w-text_w)/2-2:y=500-2,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完上古时期的三皇五帝':fontsize=71:fontcolor=0x333333@0.8:x=(w-text_w)/2+7:y=500+7,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完上古时期的三皇五帝':fontsize=71:fontcolor=0x89fd6f:x='(w-text_w)/2+5*sin(2*PI*t/85.78432888683864)':y='500+2.5*sin(2*PI*t/85.78432888683864+PI/4)'
文案内容=一口气看完上古时期的三皇五帝

Step 6/8 特效处理参数：
特效数量=0
粒子效果=无 (强度=0.00)
光效=无 (强度=0.00)
转场效果=无 (无)
文字边框=无 (宽度=0px)
高级特效数量=0

Step 7/8 音频处理参数：
变速倍数=1.0
音高微调=0.0
白噪声强度=0.01
混音方式=amix
音频采样率=44100

Step 8/8 编码转换参数：
编码格式=H.265/HEVC
输出文件=E:\视频备份\1.三皇五帝\视频处理\顺序模式_1个\输出.mp4.fixed.mp4.pseudo.mp4

[06:01:21] [伪原创] 全部8个阶段已完成！
Step 1/8 轻量视频扰动参数：
裁剪模式=no_crop
裁剪=左0px,右0px,上0px
亮度扰动=brightness:0.011
对比度扰动=contrast:1.02
镜像=否
旋转=+0.00°
色相扰动=hue:-0.91
饱和度扰动=saturation:+0.98
模糊sigma=无
缩放=1.018
平移=(4,6)
帧率扰动=30.18
边框厚度=3
锐化=0.91
噪点=轻微(强度=3)
滤镜链=noise=alls=3:allf=t,eq=brightness=0.011:contrast=1.02,hue=h=-0.91:s=0.98,scale=iw*1.018:ih*1.018,crop=iw:ih:4:6,fps=30.18,drawbox=0:0:iw:ih:0xede2e4@0.12:t=3,unsharp=5:5:0.91:5:5:0.0,scale=1920:1080,setsar=1

Step 2/8 动态滤镜链参数：
动态效果数量=4
动态色相扰动=有 (幅度=0.022, 周期=9.7s)
动态旋转=无
动态亮度/对比度渐变=有 (幅度=0.021, 周期=14.9s)
插帧=有 (60fps)
水印=有
水印图片: 已设置
水印位置: 将在Step 3中设置
水印幅度: 将在Step 3中设置
水印边距: 将在Step 3中设置
水印移动范围: 将在Step 3中设置px
水印移动速度: 将在Step 3中设置s/周期
滤镜链: hue=h=-0.07431425599222208,eq=brightness=0.009936005535072095,scale=iw*1.0239828601975656:ih*1.0239828601975656,fps=fps=31.089467,setpts=1.0*PTS,scale=1920:1080,setsar=1

Step 3/8 水印处理参数：
水印=有
水印图片: 已设置
水印位置: 将在Step 3中设置
水印幅度: 将在Step 3中设置
水印边距: 将在Step 3中设置
水印移动范围: (14,4)px
水印移动速度: 82.93s/周期

Step 4/8 视频增强参数：
RIFE视频补帧=未应用
空间变形=已应用(cx=0.4864, cy=0.5019, k1=-0.0411, k2=0.0156)
超分辨率重建=已应用(1.5倍)
HDR转换=已应用
时间超分辨率=已应用

Step 5/8 视频加速/横屏转竖屏参数：
竖屏分辨率=1080x1920
背景色=0xd5d6e4
pad滤镜=scale=1080:-1,pad=1080:1920:(ow-iw)/2:(oh-ih)/2:0xd5d6e4,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完上古时期的三皇五帝':fontsize=70:fontcolor=black:x=(w-text_w)/2+5:y=500+5,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完上古时期的三皇五帝':fontsize=70:fontcolor=black:x=(w-text_w)/2-5:y=500+5,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完上古时期的三皇五帝':fontsize=70:fontcolor=black:x=(w-text_w)/2+5:y=500-5,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完上古时期的三皇五帝':fontsize=70:fontcolor=black:x=(w-text_w)/2-5:y=500-5,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完上古时期的三皇五帝':fontsize=70:fontcolor=white:x=(w-text_w)/2+2:y=500+2,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完上古时期的三皇五帝':fontsize=70:fontcolor=white:x=(w-text_w)/2-2:y=500+2,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完上古时期的三皇五帝':fontsize=70:fontcolor=white:x=(w-text_w)/2+2:y=500-2,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完上古时期的三皇五帝':fontsize=70:fontcolor=white:x=(w-text_w)/2-2:y=500-2,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完上古时期的三皇五帝':fontsize=70:fontcolor=0x333333@0.8:x=(w-text_w)/2+7:y=500+7,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完上古时期的三皇五帝':fontsize=70:fontcolor=0xe9fecc:x='(w-text_w)/2+8*sin(2*PI*t/112.79100251384173)':y='500+4.0*sin(2*PI*t/112.79100251384173+PI/4)'
文案内容=一口气看完上古时期的三皇五帝

Step 6/8 特效处理参数：
特效数量=0
粒子效果=无 (强度=0.00)
光效=无 (强度=0.00)
转场效果=无 (无)
文字边框=无 (宽度=0px)
高级特效数量=0

Step 7/8 音频处理参数：
变速倍数=1.0
音高微调=0.0
白噪声强度=0.01
混音方式=amix
音频采样率=44100

Step 8/8 编码转换参数：
编码格式=H.265/HEVC
输出文件=E:\视频备份\1.三皇五帝\视频处理\顺序模式_1个\输出.mp4.fixed.mp4.pseudo.mp4

[06:15:29] [伪原创] 全部8个阶段已完成！
Step 1/8 轻量视频扰动参数：
裁剪模式=no_crop
裁剪=左0px,右0px,上0px
亮度扰动=brightness:0.017
对比度扰动=contrast:1.03
镜像=否
旋转=+0.00°
色相扰动=hue:-0.92
饱和度扰动=saturation:+1.03
模糊sigma=0.4
缩放=1.023
平移=(3,3)
帧率扰动=30.12
边框厚度=6
锐化=1.45
噪点=轻微(强度=8)
滤镜链=noise=alls=8:allf=t,eq=brightness=0.017:contrast=1.03,hue=h=-0.92:s=1.03,gblur=sigma=0.4,scale=iw*1.023:ih*1.023,crop=iw:ih:3:3,fps=30.12,drawbox=0:0:iw:ih:0xd7cee5@0.13:t=6,unsharp=5:5:1.45:5:5:0.0,scale=1920:1080,setsar=1

Step 2/8 动态滤镜链参数：
动态效果数量=3
动态色相扰动=有 (幅度=0.034, 周期=12.4s)
动态旋转=无
动态亮度/对比度渐变=有 (幅度=0.022, 周期=11.0s)
插帧=有 (60fps)
水印=有
水印图片: 已设置
水印位置: 将在Step 3中设置
水印幅度: 将在Step 3中设置
水印边距: 将在Step 3中设置
水印移动范围: 将在Step 3中设置px
水印移动速度: 将在Step 3中设置s/周期
滤镜链: hue=h=0.47873291700230003,eq=brightness=-0.011920060968465212,fps=fps=31.439422,setpts=1.0*PTS,scale=1920:1080,setsar=1

Step 3/8 水印处理参数：
水印=有
水印图片: 已设置
水印位置: 将在Step 3中设置
水印幅度: 将在Step 3中设置
水印边距: 将在Step 3中设置
水印移动范围: (5,4)px
水印移动速度: 86.68s/周期

Step 4/8 视频增强参数：
RIFE视频补帧=未应用
空间变形=已应用(cx=0.5135, cy=0.5079, k1=0.0205, k2=-0.0053)
超分辨率重建=已应用(1.5倍)
HDR转换=已应用
时间超分辨率=已应用

Step 5/8 视频加速/横屏转竖屏参数：
竖屏分辨率=1080x1920
背景色=0xc5dbcd
pad滤镜=scale=1080:-1,pad=1080:1920:(ow-iw)/2:(oh-ih)/2:0xc5dbcd,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完三皇五帝':fontsize=73:fontcolor=black:x=(w-text_w)/2+5:y=500+5,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完三皇五帝':fontsize=73:fontcolor=black:x=(w-text_w)/2-5:y=500+5,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完三皇五帝':fontsize=73:fontcolor=black:x=(w-text_w)/2+5:y=500-5,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完三皇五帝':fontsize=73:fontcolor=black:x=(w-text_w)/2-5:y=500-5,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完三皇五帝':fontsize=73:fontcolor=white:x=(w-text_w)/2+2:y=500+2,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完三皇五帝':fontsize=73:fontcolor=white:x=(w-text_w)/2-2:y=500+2,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完三皇五帝':fontsize=73:fontcolor=white:x=(w-text_w)/2+2:y=500-2,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完三皇五帝':fontsize=73:fontcolor=white:x=(w-text_w)/2-2:y=500-2,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完三皇五帝':fontsize=73:fontcolor=0x333333@0.8:x=(w-text_w)/2+7:y=500+7,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完三皇五帝':fontsize=73:fontcolor=0xfef1d6:x='(w-text_w)/2+3*sin(2*PI*t/98.86822838023372)':y='500+1.5*sin(2*PI*t/98.86822838023372+PI/4)'
文案内容=一口气看完三皇五帝

Step 6/8 特效处理参数：
特效数量=0
粒子效果=无 (强度=0.00)
光效=无 (强度=0.00)
转场效果=无 (无)
文字边框=无 (宽度=0px)
高级特效数量=0

Step 7/8 音频处理参数：
变速倍数=1.0
音高微调=0.0
白噪声强度=0.01
混音方式=amix
音频采样率=44100

Step 8/8 编码转换参数：
编码格式=H.265/HEVC
输出文件=E:\视频备份\1.三皇五帝\视频处理\顺序模式_1个\输出.mp4.fixed.mp4.pseudo.mp4

[09:07:51] [伪原创] 全部8个阶段已完成！
Step 1/8 轻量视频扰动参数：
裁剪模式=no_crop
裁剪=左0px,右0px,上0px
亮度扰动=brightness:0.014
对比度扰动=contrast:1.01
镜像=否
旋转=+0.00°
色相扰动=hue:-0.69
饱和度扰动=saturation:+1.00
模糊sigma=0.27
缩放=1.027
平移=(7,0)
帧率扰动=30.24
边框厚度=3
锐化=1.25
噪点=轻微(强度=6)
滤镜链=noise=alls=6:allf=t,eq=brightness=0.014:contrast=1.01,hue=h=-0.69:s=1.0,gblur=sigma=0.27,scale=iw*1.027:ih*1.027,crop=iw:ih:7:0,fps=30.24,drawbox=0:0:iw:ih:0xd8e2d3@0.14:t=3,unsharp=5:5:1.25:5:5:0.0,scale=1920:1080,setsar=1

Step 2/8 动态滤镜链参数：
动态效果数量=3
动态色相扰动=有 (幅度=0.042, 周期=11.3s)
动态旋转=无
动态亮度/对比度渐变=有 (幅度=0.022, 周期=16.8s)
插帧=有 (60fps)
水印=有
水印图片: 已设置
水印位置: 将在Step 3中设置
水印幅度: 将在Step 3中设置
水印边距: 将在Step 3中设置
水印移动范围: 将在Step 3中设置px
水印移动速度: 将在Step 3中设置s/周期
滤镜链: hue=h=0.3195822044403368,eq=brightness=0.0011552031042841061,fps=fps=31.337143,setpts=1.0*PTS,scale=1920:1080,setsar=1

Step 3/8 水印处理参数：
水印=有
水印图片: 已设置
水印位置: 将在Step 3中设置
水印幅度: 将在Step 3中设置
水印边距: 将在Step 3中设置
水印移动范围: (7,3)px
水印移动速度: 76.34s/周期

Step 4/8 视频增强参数：
RIFE视频补帧=未应用
空间变形=已应用(cx=0.4837, cy=0.4996, k1=-0.0038, k2=0.0141)
超分辨率重建=已应用(1.5倍)
HDR转换=已应用
时间超分辨率=已应用

Step 5/8 视频加速/横屏转竖屏参数：
竖屏分辨率=1080x1920
背景色=0xdadde8
pad滤镜=scale=1080:-1,pad=1080:1920:(ow-iw)/2:(oh-ih)/2:0xdadde8,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完上古时期的三皇五帝':fontsize=68:fontcolor=black:x=(w-text_w)/2+8:y=500+8,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完上古时期的三皇五帝':fontsize=68:fontcolor=black:x=(w-text_w)/2-8:y=500+8,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完上古时期的三皇五帝':fontsize=68:fontcolor=black:x=(w-text_w)/2+8:y=500-8,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完上古时期的三皇五帝':fontsize=68:fontcolor=black:x=(w-text_w)/2-8:y=500-8,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完上古时期的三皇五帝':fontsize=68:fontcolor=white:x=(w-text_w)/2+4:y=500+4,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完上古时期的三皇五帝':fontsize=68:fontcolor=white:x=(w-text_w)/2-4:y=500+4,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完上古时期的三皇五帝':fontsize=68:fontcolor=white:x=(w-text_w)/2+4:y=500-4,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完上古时期的三皇五帝':fontsize=68:fontcolor=white:x=(w-text_w)/2-4:y=500-4,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完上古时期的三皇五帝':fontsize=68:fontcolor=0x333333@0.8:x=(w-text_w)/2+10:y=500+10,drawtext=fontfile=SourceHanSansSC-Bold.otf:text='一口气看完上古时期的三皇五帝':fontsize=68:fontcolor=0xfefecf:x='(w-text_w)/2+5*sin(2*PI*t/81.99242729512838)':y='500+2.5*sin(2*PI*t/81.99242729512838+PI/4)'
文案内容=一口气看完上古时期的三皇五帝

Step 6/8 特效处理参数：
特效数量=0
粒子效果=无 (强度=0.00)
光效=无 (强度=0.00)
转场效果=无 (无)
文字边框=无 (宽度=0px)
高级特效数量=0

Step 7/8 音频处理参数：
变速倍数=1.0
音高微调=0.0
白噪声强度=0.01
混音方式=amix
音频采样率=44100

Step 8/8 编码转换参数：
编码格式=H.265/HEVC
输出文件=E:\视频备份\1.三皇五帝\视频处理\顺序模式_1个\输出.mp4.fixed.mp4.pseudo.mp4

[11:25:22] [伪原创] 全部8个阶段已完成！
Step 1/8 轻量视频扰动参数：
裁剪模式=no_crop
裁剪=左0px,右0px,上0px
亮度扰动=brightness:0.018
对比度扰动=contrast:1.01
镜像=否
旋转=+0.00°
色相扰动=hue:+0.82
饱和度扰动=saturation:+1.04
模糊sigma=0.37
缩放=1.015
平移=(-3,4)
帧率扰动=29.88
边框厚度=6
锐化=1.27
噪点=轻微(强度=4)
滤镜链=noise=alls=4:allf=t,eq=brightness=0.018:contrast=1.01,hue=h=0.82:s=1.04,gblur=sigma=0.37,scale=iw*1.015:ih*1.015,crop=iw:ih:-3:4,fps=29.88,drawbox=0:0:iw:ih:0xe2d3df@0.13:t=6,unsharp=5:5:1.27:5:5:0.0,scale=1920:1080,setsar=1

Step 2/8 动态滤镜链参数：
动态效果数量=4
动态色相扰动=有 (幅度=0.031, 周期=8.0s)
动态旋转=无
动态亮度/对比度渐变=有 (幅度=0.017, 周期=16.0s)
插帧=有 (60fps)
水印=有
水印图片: 已设置
水印位置: 将在Step 3中设置
水印幅度: 将在Step 3中设置
水印边距: 将在Step 3中设置
水印移动范围: 将在Step 3中设置px
水印移动速度: 将在Step 3中设置s/周期
滤镜链: hue=h=-0.05394511541231417,eq=brightness=0.00416643151245653,scale=iw*1.0248955693467352:ih*1.0248955693467352,fps=fps=31.299094,setpts=1.0*PTS,scale=1920:1080,setsar=1

Step 3/8 水印处理参数：
水印=有
水印图片: 已设置
水印位置: 将在Step 3中设置
水印幅度: 将在Step 3中设置
水印边距: 将在Step 3中设置
水印移动范围: (10,4)px
水印移动速度: 114.48s/周期

Step 4/8 视频增强参数：
RIFE视频补帧=未应用
空间变形=已应用(cx=0.5061, cy=0.4805, k1=0.0470, k2=0.0128)
超分辨率重建=已应用(1.5倍)
HDR转换=已应用
时间超分辨率=已应用

Step 5/8 视频加速/横屏转竖屏参数：
竖屏分辨率=1080x1920
背景色=0xdddde8
pad滤镜=scale=1080:-1,pad=1080:1920:(ow-iw)/2:(oh-ih)/2:0xdddde8
文案内容=无

Step 6/8 特效处理参数：
特效数量=0
粒子效果=无 (强度=0.00)
光效=无 (强度=0.00)
转场效果=无 (无)
文字边框=无 (宽度=0px)
高级特效数量=0

Step 7/8 音频处理参数：
变速倍数=1.0
音高微调=0.0
白噪声强度=0.01
混音方式=amix
音频采样率=44100

Step 8/8 编码转换参数：
编码格式=H.265/HEVC
输出文件=E:\视频备份\137.秦朝到清朝\视频处理\顺序模式_1个\输出.mp4.fixed.mp4.pseudo.mp4

[17:22:15] [伪原创] 全部8个阶段已完成！
Step 1/8 轻量视频扰动参数：
裁剪模式=no_crop
裁剪=左0px,右0px,上0px
亮度扰动=brightness:0.008
对比度扰动=contrast:1.02
镜像=否
旋转=+0.00°
色相扰动=hue:-1.15
饱和度扰动=saturation:+0.97
模糊sigma=0.44
缩放=1.011
平移=(-8,5)
帧率扰动=30.11
边框厚度=2
锐化=1.34
噪点=轻微(强度=7)
滤镜链=noise=alls=7:allf=t,eq=brightness=0.008:contrast=1.02,hue=h=-1.15:s=0.97,gblur=sigma=0.44,scale=iw*1.011:ih*1.011,crop=iw:ih:-8:5,fps=30.11,drawbox=0:0:iw:ih:0xbedcc7@0.12:t=2,unsharp=5:5:1.34:5:5:0.0,scale=1920:1080,setsar=1

Step 2/8 动态滤镜链参数：
动态效果数量=4
动态色相扰动=有 (幅度=0.029, 周期=9.5s)
动态旋转=无
动态亮度/对比度渐变=有 (幅度=0.027, 周期=20.0s)
插帧=有 (20个随机帧)
抽帧=有 (最多20个随机帧)
水印=有
水印图片: 已设置
水印位置: 将在Step 3中设置
水印幅度: 将在Step 3中设置
水印边距: 将在Step 3中设置
水印移动范围: 将在Step 3中设置px
水印移动速度: 将在Step 3中设置s/周期
滤镜链: hue=h=-0.1448960578606343,eq=brightness=-0.0030996806400761003,select='eq(n,61)+eq(n,217)+eq(n,277)+eq(n,507)+eq(n,646)+eq(n,719)+eq(n,927)+eq(n,1128)+eq(n,1204)+eq(n,1424)+eq(n,1520)+eq(n,1602)+eq(n,1812)+eq(n,1864)+eq(n,2131)+eq(n,2252)+eq(n,2415)+eq(n,2546)+eq(n,2622)+eq(n,2806)+eq(n,2923)+eq(n,3145)+eq(n,3298)+eq(n,3389)+eq(n,3722)+eq(n,3815)+eq(n,4013)+eq(n,4094)+eq(n,4507)+eq(n,4660)+eq(n,4709)+eq(n,4917)+eq(n,5032)+eq(n,5168)+eq(n,5314)+eq(n,5484)+eq(n,5710)+eq(n,5773)+eq(n,5967)+eq(n,6026)+eq(n,6164)+eq(n,6269)+eq(n,6458)+eq(n,6516)+eq(n,6697)+eq(n,6784)',setpts=N/FRAME_RATE/TB,select='not(eq(n,3583))*not(eq(n,4357))*not(eq(n,4374))*not(eq(n,6987))*not(eq(n,7093))',setpts=N/FRAME_RATE/TB,scale=1920:1080,setsar=1

Step 3/8 水印处理参数：
水印=有
水印图片: 已设置
水印位置: 将在Step 3中设置
水印幅度: 将在Step 3中设置
水印边距: 将在Step 3中设置
水印移动范围: (15,8)px
水印移动速度: 60.79s/周期

Step 4/8 视频增强参数：
RIFE视频补帧=未应用
空间变形=已应用(cx=0.5182, cy=0.5083, k1=-0.0119, k2=-0.0038)
超分辨率重建=已应用(1.5倍)
HDR转换=已应用
时间超分辨率=已应用

Step 5/8 视频加速/横屏转竖屏参数：
竖屏分辨率=1080x1920
背景色=0xd9cadd
pad滤镜=scale=1080:-1,pad=1080:1920:(ow-iw)/2:(oh-ih)/2:0xd9cadd,drawtext=fontfile=SourceHanSansSC-Medium.otf:text='一口气看完上古时期的三皇五帝':fontsize=68:letter_spacing=4:fontcolor=black:x=(w-text_w)/2+8:y=500+8,drawtext=fontfile=SourceHanSansSC-Medium.otf:text='一口气看完上古时期的三皇五帝':fontsize=68:letter_spacing=4:fontcolor=white:x=(w-text_w)/2+4:y=500+4,drawtext=fontfile=SourceHanSansSC-Medium.otf:text='一口气看完上古时期的三皇五帝':fontsize=68:letter_spacing=4:fontcolor=0xfecbad:x='(w-text_w)/2+3*sin(2*PI*t/101.78761344731876)':y='500+1.5*sin(2*PI*t/101.78761344731876+PI/4)'
文案内容=一口气看完上古时期的三皇五帝

Step 6/9 特效处理参数：
特效数量=0
粒子效果=无 (强度=0.00)
光效=无 (强度=0.00)
转场效果=无 (无)
文字边框=无 (宽度=0px)
高级特效数量=0

Step 7/9 动态装饰效果参数：
特效数量=未知
粒子效果=未知
光效=未知
边框=未知
装饰元素=未知

Step 8/9 音频混音处理参数：
变速倍数=1.0
音高微调=0.0
白噪声强度=0.01
混音方式=amix
音频采样率=44100

Step 9/9 编码转换参数：
编码格式=H.265/HEVC
输出文件=E:\视频备份\1.三皇五帝\视频处理\顺序模式_1个\输出.mp4.fixed.mp4.pseudo.mp4

[07:29:40] [伪原创] 全部8个阶段已完成！
Step 1/8 轻量视频扰动参数：
裁剪模式=no_crop
裁剪=左0px,右0px,上0px
亮度扰动=brightness:0.007
对比度扰动=contrast:1.01
镜像=否
旋转=+0.00°
色相扰动=hue:-1.92
饱和度扰动=saturation:+0.97
模糊sigma=0.27
缩放=1.025
平移=(6,6)
帧率扰动=29.88
边框厚度=2
锐化=1.15
噪点=轻微(强度=7)
滤镜链=noise=alls=7:allf=t,eq=brightness=0.007:contrast=1.01,hue=h=-1.92:s=0.97,gblur=sigma=0.27,scale=iw*1.025:ih*1.025,crop=iw:ih:6:6,fps=29.88,drawbox=0:0:iw:ih:0xd0d8e4@0.16:t=2,unsharp=5:5:1.15:5:5:0.0,scale=1920:1080,setsar=1

Step 2/8 动态滤镜链参数：
动态效果数量=3
动态色相扰动=有 (幅度=0.037, 周期=11.3s)
动态旋转=无
动态亮度/对比度渐变=有 (幅度=0.022, 周期=11.3s)
插帧=有 (119个随机帧)
抽帧=无
滤镜链: hue=h=0.0019086973273545471,eq=brightness=-0.00989573968954503,minterpolate=fps=65.44:mi_mode=dup,scale=1920:1080,setsar=1

Step 3/8 水印处理参数：
水印=有
水印图片: 已设置
水印位置: 将在Step 3中设置
水印幅度: 将在Step 3中设置
水印边距: 将在Step 3中设置
水印移动范围: 将在Step 3中设置px
水印移动速度: 将在Step 3中设置s/周期
滤镜链: hue=h=0.0019086973273545471,eq=brightness=-0.00989573968954503,minterpolate=fps=65.44:mi_mode=dup,scale=1920:1080,setsar=1

Step 4/8 视频增强参数：
RIFE视频补帧=未应用
空间变形=已应用(cx=0.0236, cy=0.0505, k1=0.0500, k2=0.0603)
超分辨率重建=已应用(1.5倍)
HDR转换=已应用
时间超分辨率=已应用

Step 5/8 视频加速/横屏转竖屏参数：
竖屏分辨率=1080x1920
背景色=0xe8e6d4
pad滤镜=scale=1080:-1,pad=1080:1920:(ow-iw)/2:(oh-ih)/2:0xe8e6d4,drawtext=fontfile=SourceHanSansSC-Medium.otf:text='一 口 气 看 完 三 皇 五 帝':fontsize=68:fontcolor=black:x=(w-text_w)/2+8:y=500+8,drawtext=fontfile=SourceHanSansSC-Medium.otf:text='一 口 气 看 完 三 皇 五 帝':fontsize=68:fontcolor=white:x=(w-text_w)/2+4:y=500+4,drawtext=fontfile=SourceHanSansSC-Medium.otf:text='一 口 气 看 完 三 皇 五 帝':fontsize=68:fontcolor=0x6cf7fe:x='(w-text_w)/2+6*sin(2*PI*t/112.0793396611897)':y='500+3.0*sin(2*PI*t/112.0793396611897+PI/4)'
文案内容=一口气看完三皇五帝

Step 6/9 特效处理参数：
特效数量=0
粒子效果=无 (强度=0.00)
光效=无 (强度=0.00)
转场效果=无 (无)
文字边框=无 (宽度=0px)
高级特效数量=0

Step 7/9 动态装饰效果参数：
特效数量=未知
粒子效果=未知
光效=未知
边框=未知
装饰元素=未知

Step 8/9 音频混音处理参数：
变速倍数=1.0
音高微调=0.0
白噪声强度=0.01
混音方式=amix
音频采样率=44100

Step 9/9 编码转换参数：
编码格式=H.265/HEVC
输出文件=E:\视频备份\1.三皇五帝\视频处理\片头片尾分段_1个\顺序模式_1个\视频1.mp4

[08:06:11] [伪原创] 全部8个阶段已完成！
Step 1/8 轻量视频扰动参数：
裁剪模式=no_crop
裁剪=左0px,右0px,上0px
亮度扰动=brightness:0.006
对比度扰动=contrast:1.02
镜像=否
旋转=+0.00°
色相扰动=hue:+1.27
饱和度扰动=saturation:+1.03
模糊sigma=0.27
缩放=1.029
平移=(6,1)
帧率扰动=30.19
边框厚度=2
锐化=1.38
噪点=轻微(强度=4)
滤镜链=noise=alls=4:allf=t,eq=brightness=0.006:contrast=1.02,hue=h=1.27:s=1.03,gblur=sigma=0.27,scale=iw*1.029:ih*1.029,crop=iw:ih:6:1,fps=30.19,drawbox=0:0:iw:ih:0xd9e2e7@0.14:t=2,unsharp=5:5:1.38:5:5:0.0,scale=1920:1080,setsar=1

Step 2/8 动态滤镜链参数：
动态效果数量=2
动态色相扰动=无
动态旋转=无
动态亮度/对比度渐变=有 (幅度=0.019, 周期=11.6s)
插帧=有 (119个随机帧)
抽帧=无
滤镜链: eq=brightness=-0.014019186402296144,minterpolate=fps=66.12:mi_mode=dup,scale=1920:1080,setsar=1

Step 3/8 水印处理参数：
水印=有
水印图片: 已设置
水印位置: 将在Step 3中设置
水印幅度: 将在Step 3中设置
水印边距: 将在Step 3中设置
水印移动范围: 将在Step 3中设置px
水印移动速度: 将在Step 3中设置s/周期
滤镜链: eq=brightness=-0.014019186402296144,minterpolate=fps=66.12:mi_mode=dup,scale=1920:1080,setsar=1

Step 4/8 视频增强参数：
RIFE视频补帧=未应用
空间变形=已应用(cx=0.1692, cy=0.0045, k1=0.0610, k2=0.0286)
超分辨率重建=已应用(1.5倍)
HDR转换=已应用
时间超分辨率=已应用

Step 5/8 视频加速/横屏转竖屏参数：
竖屏分辨率=1080x1920
背景色=0xe8e0d3
pad滤镜=scale=1080:-1,pad=1080:1920:(ow-iw)/2:(oh-ih)/2:0xe8e0d3,drawtext=fontfile=SourceHanSansSC-Medium.otf:text='一 口 气 看 完 上 古 时 期 的 三 皇 五 帝':fontsize=70:fontcolor=black:x=(w-text_w)/2+8:y=500+8,drawtext=fontfile=SourceHanSansSC-Medium.otf:text='一 口 气 看 完 上 古 时 期 的 三 皇 五 帝':fontsize=70:fontcolor=white:x=(w-text_w)/2+4:y=500+4,drawtext=fontfile=SourceHanSansSC-Medium.otf:text='一 口 气 看 完 上 古 时 期 的 三 皇 五 帝':fontsize=70:fontcolor=0x90fdf8:x='(w-text_w)/2+3*sin(2*PI*t/119.13963894651275)':y='500+1.5*sin(2*PI*t/119.13963894651275+PI/4)'
文案内容=一口气看完上古时期的三皇五帝

Step 6/9 特效处理参数：
特效数量=0
粒子效果=无 (强度=0.00)
光效=无 (强度=0.00)
转场效果=无 (无)
文字边框=无 (宽度=0px)
高级特效数量=0

Step 7/9 动态装饰效果参数：
特效数量=未知
粒子效果=未知
光效=未知
边框=未知
装饰元素=未知

Step 8/9 音频混音处理参数：
变速倍数=1.0
音高微调=0.0
白噪声强度=0.01
混音方式=amix
音频采样率=44100

Step 9/9 编码转换参数：
编码格式=H.265/HEVC
输出文件=E:\视频备份\1.三皇五帝\视频处理\片头片尾分段_1个\顺序模式_1个\视频1.mp4

[08:20:34] [伪原创] 全部8个阶段已完成！
Step 1/8 轻量视频扰动参数：
裁剪模式=no_crop
裁剪=左0px,右0px,上0px
亮度扰动=brightness:0.018
对比度扰动=contrast:1.02
镜像=否
旋转=+0.00°
色相扰动=hue:+1.83
饱和度扰动=saturation:+1.00
模糊sigma=0.34
缩放=1.023
平移=(6,6)
帧率扰动=30.13
边框厚度=3
锐化=1.02
噪点=轻微(强度=8)
滤镜链=noise=alls=8:allf=t,eq=brightness=0.018:contrast=1.02,hue=h=1.83:s=1.0,gblur=sigma=0.34,scale=iw*1.023:ih*1.023,crop=iw:ih:6:6,fps=30.13,drawbox=0:0:iw:ih:0xbadacb@0.12:t=3,unsharp=5:5:1.02:5:5:0.0,scale=1920:1080,setsar=1

Step 2/8 动态滤镜链参数：
动态效果数量=4
动态色相扰动=有 (幅度=0.037, 周期=10.4s)
动态旋转=无
动态亮度/对比度渐变=有 (幅度=0.018, 周期=12.6s)
插帧=有 (92个随机帧)
抽帧=无
滤镜链: hue=h=0.1939629288824689,eq=brightness=0.003480550448281678,scale=iw*1.0231363446098236:ih*1.0231363446098236,minterpolate=fps=57.85:mi_mode=dup

Step 3/8 水印处理参数：
水印=有
水印图片: 已设置
水印位置: 将在Step 3中设置
水印幅度: 将在Step 3中设置
水印边距: 将在Step 3中设置
水印移动范围: 将在Step 3中设置px
水印移动速度: 将在Step 3中设置s/周期
滤镜链: hue=h=0.1939629288824689,eq=brightness=0.003480550448281678,scale=iw*1.0231363446098236:ih*1.0231363446098236,minterpolate=fps=57.85:mi_mode=dup

Step 4/8 视频增强参数：
RIFE视频补帧=未应用
空间变形=已应用(cx=0.0660, cy=0.1198, k1=0.0100, k2=0.0747)
超分辨率重建=已应用(1.5倍)
HDR转换=已应用
时间超分辨率=已应用

Step 5/8 视频加速/横屏转竖屏参数：
竖屏分辨率=1080x1920
背景色=0xece5dc
pad滤镜=scale=1080:-1,pad=1080:1920:(ow-iw)/2:(oh-ih)/2:0xece5dc,drawtext=fontfile=SourceHanSansSC-Medium.otf:text='一 口 气 看 完 上 古 时 期 的 三 皇 五 帝':fontsize=68:fontcolor=black:x=(w-text_w)/2+8:y=500+8,drawtext=fontfile=SourceHanSansSC-Medium.otf:text='一 口 气 看 完 上 古 时 期 的 三 皇 五 帝':fontsize=68:fontcolor=white:x=(w-text_w)/2+4:y=500+4,drawtext=fontfile=SourceHanSansSC-Medium.otf:text='一 口 气 看 完 上 古 时 期 的 三 皇 五 帝':fontsize=68:fontcolor=0xfc857d:x='(w-text_w)/2+6*sin(2*PI*t/75.55695850752707)':y='500+3.0*sin(2*PI*t/75.55695850752707+PI/4)'
文案内容=一口气看完上古时期的三皇五帝

Step 6/9 特效处理参数：
特效数量=0
粒子效果=无 (强度=0.00)
光效=无 (强度=0.00)
转场效果=无 (无)
文字边框=无 (宽度=0px)
高级特效数量=0

Step 7/9 动态装饰效果参数：
特效数量=未知
粒子效果=未知
光效=未知
边框=未知
装饰元素=未知

Step 8/9 音频混音处理参数：
变速倍数=1.0
音高微调=0.0
白噪声强度=0.01
混音方式=amix
音频采样率=44100

Step 9/9 编码转换参数：
编码格式=H.265/HEVC
输出文件=E:\视频备份\1.三皇五帝\视频处理\片头片尾分段_1个\顺序模式_1个\视频1.mp4

[09:07:54] [伪原创] 全部8个阶段已完成！
Step 1/8 轻量视频扰动参数：
裁剪模式=no_crop
裁剪=左0px,右0px,上0px
亮度扰动=brightness:0.014
对比度扰动=contrast:1.01
镜像=否
旋转=+0.00°
色相扰动=hue:+0.64
饱和度扰动=saturation:+1.05
模糊sigma=0.38
缩放=1.02
平移=(7,1)
帧率扰动=29.97
边框厚度=2
锐化=1.02
噪点=轻微(强度=7)
滤镜链=noise=alls=7:allf=t,eq=brightness=0.014:contrast=1.01,hue=h=0.64:s=1.05,gblur=sigma=0.38,scale=iw*1.02:ih*1.02,crop=iw:ih:7:1,fps=29.97,drawbox=0:0:iw:ih:0xc7dac9@0.14:t=2,unsharp=5:5:1.02:5:5:0.0,scale=1920:1080,setsar=1

Step 2/8 动态滤镜链参数：
动态效果数量=4
动态色相扰动=有 (幅度=0.024, 周期=8.2s)
动态旋转=无
动态亮度/对比度渐变=无
插帧=有 (110个随机帧)
抽帧=无
滤镜链: hue=h=-0.4065853376522667,scale=iw*1.02702958735496:ih*1.02702958735496,gblur=sigma=0.7233063025747293,minterpolate=fps=62.94:mi_mode=dup

Step 3/8 水印处理参数：
水印=有
水印图片: 已设置
水印位置: 将在Step 3中设置
水印幅度: 将在Step 3中设置
水印边距: 将在Step 3中设置
水印移动范围: 将在Step 3中设置px
水印移动速度: 将在Step 3中设置s/周期
滤镜链: hue=h=-0.4065853376522667,scale=iw*1.02702958735496:ih*1.02702958735496,gblur=sigma=0.7233063025747293,minterpolate=fps=62.94:mi_mode=dup

Step 4/8 视频增强参数：
RIFE视频补帧=未应用
空间变形=已应用(cx=0.0438, cy=0.0329, k1=0.0085, k2=0.0568)
超分辨率重建=已应用(1.5倍)
HDR转换=已应用
时间超分辨率=已应用

Step 5/8 视频加速/横屏转竖屏参数：
竖屏分辨率=1080x1920
背景色=0xe7dbe1
pad滤镜=scale=1080:-1,pad=1080:1920:(ow-iw)/2:(oh-ih)/2:0xe7dbe1,drawtext=fontfile=SourceHanSansSC-Medium.otf:text='一 口 气 看 完 三 皇 五 帝':fontsize=66:fontcolor=black:x=(w-text_w)/2+8:y=500+8,drawtext=fontfile=SourceHanSansSC-Medium.otf:text='一 口 气 看 完 三 皇 五 帝':fontsize=66:fontcolor=white:x=(w-text_w)/2+4:y=500+4,drawtext=fontfile=SourceHanSansSC-Medium.otf:text='一 口 气 看 完 三 皇 五 帝':fontsize=66:fontcolor=0x77febf:x='(w-text_w)/2+8*sin(2*PI*t/86.61887043828216)':y='500+4.0*sin(2*PI*t/86.61887043828216+PI/4)'
文案内容=一口气看完三皇五帝

Step 6/9 特效处理参数：
特效数量=0
粒子效果=无 (强度=0.00)
光效=无 (强度=0.00)
转场效果=无 (无)
文字边框=无 (宽度=0px)
高级特效数量=0

Step 7/9 动态装饰效果参数：
特效数量=未知
粒子效果=未知
光效=未知
边框=未知
装饰元素=未知

Step 8/9 音频混音处理参数：
变速倍数=1.0
音高微调=0.0
白噪声强度=0.01
混音方式=amix
音频采样率=44100

Step 9/9 编码转换参数：
编码格式=H.265/HEVC
输出文件=E:\视频备份\1.三皇五帝\视频处理\片头片尾分段_1个\顺序模式_1个\视频1.mp4

[09:36:50] [伪原创] 全部8个阶段已完成！
Step 1/8 轻量视频扰动参数：
裁剪模式=no_crop
裁剪=左0px,右0px,上0px
亮度扰动=brightness:0.006
对比度扰动=contrast:1.03
镜像=否
旋转=+0.00°
色相扰动=hue:+1.58
饱和度扰动=saturation:+1.01
模糊sigma=0.49
缩放=1.013
平移=(-6,5)
帧率扰动=30.18
边框厚度=2
锐化=0.81
噪点=轻微(强度=8)
滤镜链=noise=alls=8:allf=t,eq=brightness=0.006:contrast=1.03,hue=h=1.58:s=1.01,gblur=sigma=0.49,scale=iw*1.013:ih*1.013,crop=iw:ih:-6:5,fps=30.18,drawbox=0:0:iw:ih:0xe4dcd2@0.11:t=2,unsharp=5:5:0.81:5:5:0.0,scale=1920:1080,setsar=1

Step 2/8 动态滤镜链参数：
动态效果数量=3
动态色相扰动=有 (幅度=0.027, 周期=14.2s)
动态旋转=无
动态亮度/对比度渐变=有 (幅度=0.020, 周期=15.4s)
插帧=有 (113个随机帧)
抽帧=无
滤镜链: hue=h=-0.317192959654795,eq=brightness=0.0025005307985623317,minterpolate=fps=64.28:mi_mode=dup,scale=1920:1080,setsar=1

Step 3/8 水印处理参数：
水印=有
水印图片: 已设置
水印位置: 将在Step 3中设置
水印幅度: 将在Step 3中设置
水印边距: 将在Step 3中设置
水印移动范围: 将在Step 3中设置px
水印移动速度: 将在Step 3中设置s/周期
滤镜链: hue=h=-0.317192959654795,eq=brightness=0.0025005307985623317,minterpolate=fps=64.28:mi_mode=dup,scale=1920:1080,setsar=1

Step 4/8 视频增强参数：
RIFE视频补帧=未应用
空间变形=已应用(cx=0.0543, cy=0.0180, k1=0.0121, k2=0.0302)
超分辨率重建=已应用(1.5倍)
HDR转换=已应用
时间超分辨率=已应用

Step 5/8 视频加速/横屏转竖屏参数：
竖屏分辨率=1080x1920
背景色=0xc4dbdb
pad滤镜=scale=1080:-1,pad=1080:1920:(ow-iw)/2:(oh-ih)/2:0xc4dbdb,drawtext=fontfile=SourceHanSansSC-Medium.otf:text='一 口 气 看 完 三 皇 五 帝':fontsize=73:fontcolor=black:x=(w-text_w)/2+8:y=500+8,drawtext=fontfile=SourceHanSansSC-Medium.otf:text='一 口 气 看 完 三 皇 五 帝':fontsize=73:fontcolor=white:x=(w-text_w)/2+4:y=500+4,drawtext=fontfile=SourceHanSansSC-Medium.otf:text='一 口 气 看 完 三 皇 五 帝':fontsize=73:fontcolor=0xb179fc:x='(w-text_w)/2+3*sin(2*PI*t/76.3679780533196)':y='500+1.5*sin(2*PI*t/76.3679780533196+PI/4)'
文案内容=一口气看完三皇五帝

Step 6/9 特效处理参数：
特效数量=0
粒子效果=无 (强度=0.00)
光效=无 (强度=0.00)
转场效果=无 (无)
文字边框=无 (宽度=0px)
高级特效数量=0

Step 7/9 动态装饰效果参数：
特效数量=未知
粒子效果=未知
光效=未知
边框=未知
装饰元素=未知

Step 8/9 音频混音处理参数：
变速倍数=1.0
音高微调=0.0
白噪声强度=0.01
混音方式=amix
音频采样率=44100

Step 9/9 编码转换参数：
编码格式=H.265/HEVC
输出文件=E:\视频备份\1.三皇五帝\视频处理\片头片尾分段_1个\顺序模式_1个\视频1.mp4

[10:17:40] [伪原创] 全部8个阶段已完成！
Step 1/8 轻量视频扰动参数：
裁剪模式=no_crop
裁剪=左0px,右0px,上0px
亮度扰动=brightness:0.017
对比度扰动=contrast:1.02
镜像=否
旋转=+0.00°
色相扰动=hue:-1.19
饱和度扰动=saturation:+0.99
模糊sigma=0.5
缩放=1.01
平移=(0,5)
帧率扰动=30.12
边框厚度=2
锐化=1.19
噪点=轻微(强度=4)
滤镜链=noise=alls=4:allf=t,eq=brightness=0.017:contrast=1.02,hue=h=-1.19:s=0.99,gblur=sigma=0.5,scale=iw*1.01:ih*1.01,crop=iw:ih:0:5,fps=30.12,drawbox=0:0:iw:ih:0xd5cabe@0.09:t=2,unsharp=5:5:1.19:5:5:0.0,scale=1920:1080,setsar=1

Step 2/8 动态滤镜链参数：
动态效果数量=3
动态色相扰动=无
动态旋转=无
动态亮度/对比度渐变=有 (幅度=0.023, 周期=11.3s)
插帧=有 (103个随机帧)
抽帧=无
滤镜链: eq=brightness=0.012162572838720551,scale=iw*1.0264450648559351:ih*1.0264450648559351,minterpolate=fps=61.14:mi_mode=dup

Step 3/8 水印处理参数：
水印=有
水印图片: 已设置
水印位置: 将在Step 3中设置
水印幅度: 将在Step 3中设置
水印边距: 将在Step 3中设置
水印移动范围: 将在Step 3中设置px
水印移动速度: 将在Step 3中设置s/周期
滤镜链: eq=brightness=0.012162572838720551,scale=iw*1.0264450648559351:ih*1.0264450648559351,minterpolate=fps=61.14:mi_mode=dup

Step 4/8 视频增强参数：
RIFE视频补帧=未应用
空间变形=已应用(cx=0.0105, cy=0.1410, k1=0.0989, k2=0.0086)
超分辨率重建=已应用(1.5倍)
HDR转换=已应用
时间超分辨率=已应用

Step 5/8 视频加速/横屏转竖屏参数：
竖屏分辨率=1080x1920
背景色=0xdee9e9
pad滤镜=scale=1080:-1,pad=1080:1920:(ow-iw)/2:(oh-ih)/2:0xdee9e9,drawtext=fontfile=SourceHanSansSC-Medium.otf:text='一 口 气 看 完 三 皇 五 帝':fontsize=73:fontcolor=black:x=(w-text_w)/2+8:y=500+8,drawtext=fontfile=SourceHanSansSC-Medium.otf:text='一 口 气 看 完 三 皇 五 帝':fontsize=73:fontcolor=white:x=(w-text_w)/2+4:y=500+4,drawtext=fontfile=SourceHanSansSC-Medium.otf:text='一 口 气 看 完 三 皇 五 帝':fontsize=73:fontcolor=0xc5c8fe:x='(w-text_w)/2+3*sin(2*PI*t/79.3260944988659)':y='500+1.5*sin(2*PI*t/79.3260944988659+PI/4)'
文案内容=一口气看完三皇五帝

Step 6/9 特效处理参数：
特效数量=0
粒子效果=无 (强度=0.00)
光效=无 (强度=0.00)
转场效果=无 (无)
文字边框=无 (宽度=0px)
高级特效数量=0

Step 7/9 动态装饰效果参数：
特效数量=未知
粒子效果=未知
光效=未知
边框=未知
装饰元素=未知

Step 8/9 音频混音处理参数：
变速倍数=1.0
音高微调=0.0
白噪声强度=0.01
混音方式=amix
音频采样率=44100

Step 9/9 编码转换参数：
编码格式=H.265/HEVC
输出文件=E:\视频备份\1.三皇五帝\视频处理\片头片尾分段_1个\顺序模式_1个\视频1.mp4

[11:35:09] [伪原创] 全部8个阶段已完成！
Step 1/8 轻量视频扰动参数：
裁剪模式=no_crop
裁剪=左0px,右0px,上0px
亮度扰动=brightness:0.007
对比度扰动=contrast:1.01
镜像=否
旋转=+0.00°
色相扰动=hue:-0.73
饱和度扰动=saturation:+0.95
模糊sigma=0.28
缩放=1.026
平移=(6,5)
帧率扰动=30.13
边框厚度=6
锐化=1.26
噪点=轻微(强度=8)
滤镜链=noise=alls=8:allf=t,eq=brightness=0.007:contrast=1.01,hue=h=-0.73:s=0.95,gblur=sigma=0.28,scale=iw*1.026:ih*1.026,crop=iw:ih:6:5,fps=30.13,drawbox=0:0:iw:ih:0xd7bfd2@0.09:t=6,unsharp=5:5:1.26:5:5:0.0,scale=1920:1080,setsar=1

Step 2/8 动态滤镜链参数：
动态效果数量=4
动态色相扰动=有 (幅度=0.022, 周期=14.5s)
动态旋转=无
动态亮度/对比度渐变=有 (幅度=0.023, 周期=17.3s)
插帧=有 (88个随机帧)
抽帧=无
滤镜链: hue=h=-0.06418287468012618,eq=brightness=-0.005188958850002128,gblur=sigma=0.9416179087725329,minterpolate=fps=56.64:mi_mode=dup,scale=1920:1080,setsar=1

Step 3/8 水印处理参数：
水印=有
水印图片: 已设置
水印位置: 将在Step 3中设置
水印幅度: 将在Step 3中设置
水印边距: 将在Step 3中设置
水印移动范围: 将在Step 3中设置px
水印移动速度: 将在Step 3中设置s/周期
滤镜链: hue=h=-0.06418287468012618,eq=brightness=-0.005188958850002128,gblur=sigma=0.9416179087725329,minterpolate=fps=56.64:mi_mode=dup,scale=1920:1080,setsar=1

Step 4/8 视频增强参数：
RIFE视频补帧=未应用
空间变形=已应用(cx=0.0168, cy=0.0756, k1=0.0788, k2=0.0388)
超分辨率重建=已应用(1.5倍)
HDR转换=已应用
时间超分辨率=已应用

Step 5/8 视频加速/横屏转竖屏参数：
竖屏分辨率=1080x1920
背景色=0xbbd8d2
pad滤镜=scale=1080:-1,pad=1080:1920:(ow-iw)/2:(oh-ih)/2:0xbbd8d2,drawtext=fontfile=SourceHanSansSC-Medium.otf:text='一 口 气 看 完 三 皇 五 帝':fontsize=66:fontcolor=black:x=(w-text_w)/2+8:y=500+8,drawtext=fontfile=SourceHanSansSC-Medium.otf:text='一 口 气 看 完 三 皇 五 帝':fontsize=66:fontcolor=white:x=(w-text_w)/2+4:y=500+4,drawtext=fontfile=SourceHanSansSC-Medium.otf:text='一 口 气 看 完 三 皇 五 帝':fontsize=66:fontcolor=0x8bfafd:x='(w-text_w)/2+5*sin(2*PI*t/63.187678584460095)':y='500+2.5*sin(2*PI*t/63.187678584460095+PI/4)'
文案内容=一口气看完三皇五帝

Step 6/9 特效处理参数：
特效数量=0
粒子效果=无 (强度=0.00)
光效=无 (强度=0.00)
转场效果=无 (无)
文字边框=无 (宽度=0px)
高级特效数量=0

Step 7/9 动态装饰效果参数：
特效数量=未知
粒子效果=未知
光效=未知
边框=未知
装饰元素=未知

Step 8/9 音频混音处理参数：
变速倍数=1.0
音高微调=0.0
白噪声强度=0.01
混音方式=amix
音频采样率=44100

Step 9/9 编码转换参数：
编码格式=H.265/HEVC
输出文件=E:\视频备份\1.三皇五帝\视频处理\片头片尾分段_1个\顺序模式_1个\视频1.mp4

[11:51:58] [伪原创] 全部8个阶段已完成！
Step 1/8 轻量视频扰动参数：
裁剪模式=no_crop
裁剪=左0px,右0px,上0px
亮度扰动=brightness:0.020
对比度扰动=contrast:1.02
镜像=否
旋转=+0.00°
色相扰动=hue:+1.84
饱和度扰动=saturation:+0.98
模糊sigma=0.41
缩放=1.03
平移=(1,0)
帧率扰动=29.79
边框厚度=2
锐化=0.95
噪点=轻微(强度=8)
滤镜链=noise=alls=8:allf=t,eq=brightness=0.020:contrast=1.02,hue=h=1.84:s=0.98,gblur=sigma=0.41,scale=iw*1.03:ih*1.03,crop=iw:ih:1:0,fps=29.79,drawbox=0:0:iw:ih:0xe9ede1@0.18:t=2,unsharp=5:5:0.95:5:5:0.0,scale=1920:1080,setsar=1

Step 2/8 动态滤镜链参数：
动态效果数量=3
动态色相扰动=有 (幅度=0.042, 周期=13.6s)
动态旋转=无
动态亮度/对比度渐变=无
插帧=有 (89个随机帧)
抽帧=无
滤镜链: hue=h=-0.43866085954777356,gblur=sigma=0.7696400888792336,minterpolate=fps=56.30:mi_mode=dup,scale=1920:1080,setsar=1

Step 3/8 水印处理参数：
水印=有
水印图片: 已设置
水印位置: 将在Step 3中设置
水印幅度: 将在Step 3中设置
水印边距: 将在Step 3中设置
水印移动范围: 将在Step 3中设置px
水印移动速度: 将在Step 3中设置s/周期
滤镜链: hue=h=-0.43866085954777356,gblur=sigma=0.7696400888792336,minterpolate=fps=56.30:mi_mode=dup,scale=1920:1080,setsar=1

Step 4/8 视频增强参数：
RIFE视频补帧=未应用
空间变形=已应用(cx=0.1835, cy=0.1447, k1=0.0780, k2=0.0117)
超分辨率重建=已应用(1.5倍)
HDR转换=已应用
时间超分辨率=已应用

Step 5/8 视频加速/横屏转竖屏参数：
竖屏分辨率=1080x1920
背景色=0xdacdc2
pad滤镜=scale=1080:-1,pad=1080:1920:(ow-iw)/2:(oh-ih)/2:0xdacdc2,drawtext=fontfile=SourceHanSansSC-Medium.otf:text='一 口 气 看 完 三 皇 五 帝':fontsize=68:fontcolor=black:x=(w-text_w)/2+8:y=500+8,drawtext=fontfile=SourceHanSansSC-Medium.otf:text='一 口 气 看 完 三 皇 五 帝':fontsize=68:fontcolor=white:x=(w-text_w)/2+4:y=500+4,drawtext=fontfile=SourceHanSansSC-Medium.otf:text='一 口 气 看 完 三 皇 五 帝':fontsize=68:fontcolor=0xfed2a5:x='(w-text_w)/2+8*sin(2*PI*t/113.89118816490915)':y='500+4.0*sin(2*PI*t/113.89118816490915+PI/4)'
文案内容=一口气看完三皇五帝

Step 6/9 特效处理参数：
特效数量=0
粒子效果=无 (强度=0.00)
光效=无 (强度=0.00)
转场效果=无 (无)
文字边框=无 (宽度=0px)
高级特效数量=0

Step 7/9 动态装饰效果参数：
特效数量=未知
粒子效果=未知
光效=未知
边框=未知
装饰元素=未知

Step 8/9 音频混音处理参数：
变速倍数=1.0
音高微调=0.0
白噪声强度=0.01
混音方式=amix
音频采样率=44100

Step 9/9 编码转换参数：
编码格式=H.265/HEVC
输出文件=E:\视频备份\1.三皇五帝\视频处理\片头片尾分段_1个\顺序模式_1个\视频1.mp4

[12:15:30] [伪原创] 全部8个阶段已完成！
Step 1/8 轻量视频扰动参数：
裁剪模式=no_crop
裁剪=左0px,右0px,上0px
亮度扰动=brightness:0.010
对比度扰动=contrast:1.02
镜像=否
旋转=+0.00°
色相扰动=hue:-0.70
饱和度扰动=saturation:+0.95
模糊sigma=0.46
缩放=1.026
平移=(8,4)
帧率扰动=29.91
边框厚度=5
锐化=0.8
噪点=轻微(强度=6)
滤镜链=noise=alls=6:allf=t,eq=brightness=0.010:contrast=1.02,hue=h=-0.7:s=0.95,gblur=sigma=0.46,scale=iw*1.026:ih*1.026,crop=iw:ih:8:4,fps=29.91,drawbox=0:0:iw:ih:0xc5cde0@0.14:t=5,unsharp=5:5:0.8:5:5:0.0,scale=1920:1080,setsar=1

Step 2/8 动态滤镜链参数：
动态效果数量=4
动态色相扰动=有 (幅度=0.025, 周期=8.0s)
动态旋转=无
动态亮度/对比度渐变=有 (幅度=0.020, 周期=14.4s)
插帧=有 (98个随机帧)
抽帧=无
滤镜链: hue=h=0.3165318128572643,eq=brightness=0.017108235446833212,scale=iw*1.0218020596276292:ih*1.0218020596276292,minterpolate=fps=59.22:mi_mode=dup

Step 3/8 水印处理参数：
水印=有
水印图片: 已设置
水印位置: 将在Step 3中设置
水印幅度: 将在Step 3中设置
水印边距: 将在Step 3中设置
水印移动范围: 将在Step 3中设置px
水印移动速度: 将在Step 3中设置s/周期
滤镜链: hue=h=0.3165318128572643,eq=brightness=0.017108235446833212,scale=iw*1.0218020596276292:ih*1.0218020596276292,minterpolate=fps=59.22:mi_mode=dup

Step 4/8 视频增强参数：
RIFE视频补帧=未应用
空间变形=已应用(cx=0.0844, cy=0.0245, k1=0.0511, k2=0.0210)
超分辨率重建=已应用(1.5倍)
HDR转换=已应用
时间超分辨率=已应用

Step 5/8 视频加速/横屏转竖屏参数：
竖屏分辨率=1080x1920
背景色=0xe0e7ee
pad滤镜=scale=1080:-1,pad=1080:1920:(ow-iw)/2:(oh-ih)/2:0xe0e7ee,drawtext=fontfile=SourceHanSansSC-Medium.otf:text='一 口 气 看 完 上 古 时 期 的 三 皇 五 帝':fontsize=67:fontcolor=black:x=(w-text_w)/2+8:y=500+8,drawtext=fontfile=SourceHanSansSC-Medium.otf:text='一 口 气 看 完 上 古 时 期 的 三 皇 五 帝':fontsize=67:fontcolor=white:x=(w-text_w)/2+4:y=500+4,drawtext=fontfile=SourceHanSansSC-Medium.otf:text='一 口 气 看 完 上 古 时 期 的 三 皇 五 帝':fontsize=67:fontcolor=0xf681fc:x='(w-text_w)/2+3*sin(2*PI*t/108.76420629055684)':y='500+1.5*sin(2*PI*t/108.76420629055684+PI/4)'
文案内容=一口气看完上古时期的三皇五帝

Step 6/9 特效处理参数：
特效数量=0
粒子效果=无 (强度=0.00)
光效=无 (强度=0.00)
转场效果=无 (无)
文字边框=无 (宽度=0px)
高级特效数量=0

Step 7/9 动态装饰效果参数：
特效数量=未知
粒子效果=未知
光效=未知
边框=未知
装饰元素=未知

Step 8/9 音频混音处理参数：
变速倍数=1.0
音高微调=0.0
白噪声强度=0.01
混音方式=amix
音频采样率=44100

Step 9/9 编码转换参数：
编码格式=H.265/HEVC
输出文件=E:\视频备份\1.三皇五帝\视频处理\片头片尾分段_1个\顺序模式_1个\视频1.mp4

[01:06:52] [S1-基础处理] 执行ffprobe命令: ffprobe -v error -print_format json -show_format -show_streams E:\视频处理\1.三皇五帝\伪原创_视频.mp4.fixed.mp4
[01:06:52] [S1-基础处理] 执行ffprobe命令: ffprobe -v error -print_format json -show_format -show_streams E:\视频处理\1.三皇五帝\伪原创_视频.mp4.fixed.mp4
[01:06:53] [视频处理] 执行ffprobe命令: ffprobe -v error -print_format json -show_format -show_streams E:\视频处理\1.三皇五帝\伪原创_视频.mp4.fixed.mp4
[01:06:53] [S1-基础处理] 执行命令: ffmpeg -hwaccel cuda -c:v h264_cuvid -y -i E:\视频处理\1.三皇五帝\伪原创_视频.mp4.fixed.mp4 -vf noise=alls=8:allf=t,eq=brightness=0.012:contrast=1.03,hue=h=-1.36:s=0.96,scale=iw*1.021:ih*1.021,crop=iw:ih:-3:6,drawbox=0:0:iw:ih:0xdeddcd@0.12:t=2,unsharp=5:5:1.13:5:5:0.0,scale=1920:1080,setsar=1 -c:v h264_nvenc -pix_fmt yuv420p -b:v 15000k -preset p1 -c:a copy -map 0:v:0 -map 0:a:0? -avoid_negative_ts disabled -vsync passthrough -async 1 -copyts E:\视频处理\1.三皇五帝\伪原创_视频.mp4.fixed.step1.mp4
[01:09:11] [S1-基础处理] 执行ffprobe命令: ffprobe -v error -print_format json -show_format -show_streams E:\视频处理\1.三皇五帝\伪原创_视频.mp4.fixed.step1.mp4
[01:09:12] [S2-滤镜处理] 执行ffprobe命令: ffprobe -v error -print_format json -show_format -show_streams E:\视频处理\1.三皇五帝\伪原创_视频.mp4.fixed.step1.mp4
[01:09:12] [S2-滤镜处理] 执行ffprobe命令: ffprobe -v error -print_format json -show_format -show_streams E:\视频处理\1.三皇五帝\伪原创_视频.mp4.fixed.step1.mp4
[01:09:13] [视频处理] 执行ffprobe命令: ffprobe -v error -print_format json -show_format -show_streams E:\视频处理\1.三皇五帝\伪原创_视频.mp4.fixed.step1.mp4
[01:09:14] [视频处理] 执行ffprobe命令: ffprobe -v error -print_format json -show_format -show_streams E:\视频处理\1.三皇五帝\伪原创_视频.mp4.fixed.step1.mp4
[01:09:14] [视频处理] 执行ffprobe命令: ffprobe -v error -print_format json -show_format -show_streams E:\视频处理\1.三皇五帝\伪原创_视频.mp4.fixed.step1.mp4
[01:10:51] [S3-水印处理] 执行ffprobe命令: ffprobe -v error -print_format json -show_format -show_streams E:\视频处理\1.三皇五帝\伪原创_视频.mp4.fixed.step2.mp4
[01:12:26] [视频处理] 执行ffprobe命令: ffprobe -v error -print_format json -show_format -show_streams E:\视频处理\1.三皇五帝\伪原创_视频.mp4.fixed.step2.mp4
[01:12:26] [视频处理] 执行ffprobe命令: ffprobe -v error -print_format json -show_format -show_streams E:\视频处理\1.三皇五帝\伪原创_视频.mp4.fixed.step3.mp4
[01:12:28] [S4-视频增强] 执行ffprobe命令: ffprobe -v error -print_format json -show_format -show_streams E:\视频处理\1.三皇五帝\伪原创_视频.mp4.fixed.step3.mp4
[01:12:29] [视频处理] 执行ffprobe命令: ffprobe -v error -print_format json -show_format -show_streams E:\视频处理\1.三皇五帝\伪原创_视频.mp4.fixed.step3.mp4
[01:12:29] [视频处理] 执行ffprobe命令: ffprobe -v error -print_format json -show_format -show_streams E:\视频处理\1.三皇五帝\伪原创_视频.mp4.fixed.step3.mp4
[01:19:41] [S5-特效处理] 执行ffprobe命令: ffprobe -v error -print_format json -show_format -show_streams E:\视频处理\1.三皇五帝\伪原创_视频.mp4.fixed.step4.mp4
[01:21:13] [S6-动态装饰效果] 执行ffprobe命令: ffprobe -v error -print_format json -show_format -show_streams E:\视频处理\1.三皇五帝\伪原创_视频.mp4.fixed.step5.mp4
[01:22:26] [S7-横竖屏处理] 执行ffprobe命令: ffprobe -v error -print_format json -show_format -show_streams E:\视频处理\1.三皇五帝\伪原创_视频.mp4.fixed.step6.mp4
