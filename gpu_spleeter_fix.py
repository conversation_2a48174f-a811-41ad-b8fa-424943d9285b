#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
GPU模式Spleeter修复工具

专门解决GPU模式下的TensorFlow兼容性问题，不降级到CPU
"""

import os
import sys
import subprocess

def check_gpu_environment():
    """检查GPU环境"""
    print("🔍 检查GPU环境...")
    
    try:
        import tensorflow as tf
        print(f"✅ TensorFlow版本: {tf.__version__}")
        
        # 检查GPU设备
        gpus = tf.config.list_physical_devices('GPU')
        if gpus:
            print(f"✅ 检测到 {len(gpus)} 个GPU设备:")
            for i, gpu in enumerate(gpus):
                print(f"   GPU {i}: {gpu}")
                
            # 获取GPU详细信息
            try:
                gpu_details = tf.config.experimental.get_device_details(gpus[0])
                print(f"   GPU详情: {gpu_details}")
            except:
                pass
                
            return True, gpus
        else:
            print("❌ 未检测到GPU设备")
            return False, []
            
    except Exception as e:
        print(f"❌ GPU环境检查失败: {e}")
        return False, []

def apply_gpu_memory_fix():
    """应用GPU内存修复"""
    print("\n🔧 应用GPU内存优化...")
    
    try:
        import tensorflow as tf
        
        gpus = tf.config.list_physical_devices('GPU')
        if gpus:
            for gpu in gpus:
                # 启用内存增长
                tf.config.experimental.set_memory_growth(gpu, True)
                print(f"✅ 为 {gpu} 启用内存增长模式")
                
                # 设置内存限制（可选）
                # tf.config.experimental.set_memory_limit(gpu, 4096)  # 4GB限制
                
        return True
    except Exception as e:
        print(f"❌ GPU内存优化失败: {e}")
        return False

def set_gpu_environment_variables():
    """设置GPU优化环境变量"""
    print("\n🔧 设置GPU优化环境变量...")
    
    # GPU优化环境变量
    gpu_env_vars = {
        'TF_FORCE_GPU_ALLOW_GROWTH': 'true',
        # 移除 cuda_malloc_async 避免与Spleeter的GPU流管理冲突
        # 'TF_GPU_ALLOCATOR': 'cuda_malloc_async',  # 使用异步内存分配器
        'TF_CPP_MIN_LOG_LEVEL': '1',  # 显示警告信息
        'CUDA_CACHE_DISABLE': '0',  # 启用CUDA缓存
        'TF_ENABLE_GPU_GARBAGE_COLLECTION': 'true',  # 启用GPU垃圾回收
    }
    
    for key, value in gpu_env_vars.items():
        os.environ[key] = value
        print(f"✅ 设置 {key} = {value}")
    
    # 确保不强制使用CPU
    if 'CUDA_VISIBLE_DEVICES' in os.environ:
        if os.environ['CUDA_VISIBLE_DEVICES'] == '-1':
            del os.environ['CUDA_VISIBLE_DEVICES']
            print("✅ 移除CPU强制设置")

def test_spleeter_gpu_advanced():
    """高级GPU模式测试"""
    print("\n🧪 测试Spleeter GPU模式（高级）...")
    
    try:
        # 应用所有GPU优化
        set_gpu_environment_variables()
        apply_gpu_memory_fix()
        
        # 重新导入以应用设置
        if 'spleeter.separator' in sys.modules:
            del sys.modules['spleeter.separator']
        
        from spleeter.separator import Separator
        
        print("   正在加载2stems模型...")
        separator = Separator('spleeter:2stems-16kHz')
        print("✅ Spleeter GPU模式测试成功！")
        
        return True
        
    except Exception as e:
        error_str = str(e).lower()
        print(f"❌ GPU模式测试失败: {e}")
        
        # 分析错误类型并提供具体建议
        if "unimplementederror" in error_str:
            print("\n💡 UnimplementedError解决方案:")
            print("1. 检查CUDA版本兼容性")
            print("2. 尝试降级TensorFlow到兼容版本")
            print("3. 更新GPU驱动程序")
        elif "out of memory" in error_str or "memory" in error_str:
            print("\n💡 内存不足解决方案:")
            print("1. 关闭其他占用GPU内存的程序")
            print("2. 减少批处理大小")
            print("3. 启用GPU内存增长模式")
        elif "cuda" in error_str:
            print("\n💡 CUDA问题解决方案:")
            print("1. 重新安装CUDA和cuDNN")
            print("2. 检查CUDA版本与TensorFlow兼容性")
            print("3. 更新NVIDIA驱动")
            
        return False

def create_gpu_optimized_startup():
    """创建GPU优化启动脚本"""
    script_content = '''@echo off
echo 正在启动视频处理程序（GPU优化模式）...

REM GPU优化环境变量
set TF_FORCE_GPU_ALLOW_GROWTH=true
set TF_GPU_ALLOCATOR=cuda_malloc_async
set TF_CPP_MIN_LOG_LEVEL=1
set CUDA_CACHE_DISABLE=0
set TF_ENABLE_GPU_GARBAGE_COLLECTION=true

REM 确保使用GPU（移除CPU强制设置）
set CUDA_VISIBLE_DEVICES=

echo GPU优化设置完成，启动程序...
python main.py

if errorlevel 1 (
    echo.
    echo 程序异常退出，请检查错误信息
    echo 如果是GPU相关错误，请运行 python gpu_spleeter_fix.py 获取详细诊断
)

pause
'''
    
    with open('start_gpu_optimized.bat', 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print("✅ 创建GPU优化启动脚本: start_gpu_optimized.bat")

def provide_tensorflow_cuda_compatibility():
    """提供TensorFlow-CUDA兼容性信息"""
    print("\n" + "=" * 60)
    print("📋 TensorFlow-CUDA兼容性参考")
    print("=" * 60)
    
    compatibility_table = [
        ("TensorFlow 2.8.x", "CUDA 11.2", "cuDNN 8.1"),
        ("TensorFlow 2.9.x", "CUDA 11.2", "cuDNN 8.1"),
        ("TensorFlow 2.10.x", "CUDA 11.2", "cuDNN 8.1"),
        ("TensorFlow 2.11.x", "CUDA 11.2", "cuDNN 8.1"),
    ]
    
    print("推荐的版本组合:")
    for tf_ver, cuda_ver, cudnn_ver in compatibility_table:
        print(f"  {tf_ver:<20} + {cuda_ver:<12} + {cudnn_ver}")
    
    print(f"\n当前环境:")
    try:
        import tensorflow as tf
        print(f"  TensorFlow: {tf.__version__}")
    except:
        print("  TensorFlow: 未安装")
    
    # 检查CUDA版本
    try:
        result = subprocess.run(['nvidia-smi'], capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            for line in lines:
                if 'CUDA Version:' in line:
                    cuda_version = line.split('CUDA Version:')[1].strip().split()[0]
                    print(f"  CUDA: {cuda_version}")
                    break
        else:
            print("  CUDA: 检测失败")
    except:
        print("  CUDA: 未安装或不在PATH中")

def provide_gpu_troubleshooting():
    """提供GPU故障排除指南"""
    print("\n" + "=" * 60)
    print("🔧 GPU故障排除指南")
    print("=" * 60)
    
    print("如果仍然遇到GPU问题，请按顺序尝试:")
    print()
    print("1. 🔄 重启相关服务:")
    print("   - 重启Python进程")
    print("   - 重启NVIDIA显示驱动服务")
    print()
    print("2. 🔧 更新驱动和CUDA:")
    print("   - 下载最新NVIDIA驱动")
    print("   - 重新安装CUDA Toolkit")
    print("   - 重新安装cuDNN")
    print()
    print("3. 🔧 重新安装TensorFlow:")
    print("   pip uninstall tensorflow")
    print("   pip install tensorflow==2.8.4")
    print()
    print("4. 🔧 验证安装:")
    print("   python -c \"import tensorflow as tf; print(tf.config.list_physical_devices('GPU'))\"")
    print()
    print("5. 🔧 如果问题持续:")
    print("   - 检查Windows事件查看器中的错误")
    print("   - 运行 nvidia-smi 检查GPU状态")
    print("   - 检查GPU温度和功耗")

def main():
    """主函数"""
    print("=" * 60)
    print("🎯 GPU模式Spleeter修复工具")
    print("=" * 60)
    
    # 检查GPU环境
    gpu_ok, gpus = check_gpu_environment()
    
    if not gpu_ok:
        print("\n❌ GPU环境检查失败")
        provide_tensorflow_cuda_compatibility()
        provide_gpu_troubleshooting()
        return
    
    # 测试Spleeter GPU模式
    if test_spleeter_gpu_advanced():
        print("\n🎉 GPU模式修复成功！")
        print("您的GPU可以正常运行Spleeter")
        
        # 创建优化启动脚本
        create_gpu_optimized_startup()
        
        print("\n📋 使用建议:")
        print("1. 使用 start_gpu_optimized.bat 启动程序")
        print("2. GPU内存增长模式已启用")
        print("3. 异步内存分配器已启用")
        print("4. GPU垃圾回收已启用")
        
    else:
        print("\n❌ GPU模式仍有问题")
        provide_tensorflow_cuda_compatibility()
        provide_gpu_troubleshooting()
        
        # 仍然创建启动脚本，以便用户尝试
        create_gpu_optimized_startup()
        print("\n💡 已创建优化启动脚本，您可以尝试使用")

if __name__ == "__main__":
    main()
