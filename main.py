import os
import sys
import io
import subprocess
from typing import Any

# 在最开始就设置GPU兼容性环境变量
def setup_gpu_environment():
    """设置GPU兼容性环境变量，解决CUDA 12.x与TensorFlow兼容性问题"""
    gpu_env_vars = {
        'TF_FORCE_GPU_ALLOW_GROWTH': 'true',
        # 移除 cuda_malloc_async 避免与Spleeter的GPU流管理冲突
        # 'TF_GPU_ALLOCATOR': 'cuda_malloc_async',
        'CUDA_MODULE_LOADING': 'LAZY',
        'TF_CUDA_COMPUTE_CAPABILITIES': '8.6',  # RTX 3060
        'TF_GPU_THREAD_MODE': 'gpu_private',
        'TF_GPU_THREAD_COUNT': '2',
        'TF_CPP_MIN_LOG_LEVEL': '1',
        'TF_ENABLE_ONEDNN_OPTS': '0',
        'TF_DISABLE_MKL': '1',
        'CUDA_CACHE_DISABLE': '0',
        'TF_ENABLE_GPU_GARBAGE_COLLECTION': 'true'
    }

    for key, value in gpu_env_vars.items():
        os.environ[key] = value

    # 确保不强制使用CPU
    if 'CUDA_VISIBLE_DEVICES' in os.environ and os.environ['CUDA_VISIBLE_DEVICES'] == '-1':
        del os.environ['CUDA_VISIBLE_DEVICES']

# 设置GPU环境
setup_gpu_environment()

# 在最开始就设置完全静默模式
if sys.platform.startswith('win'):
    try:
        # 设置环境变量禁用各种输出
        os.environ['QT_LOGGING_RULES'] = '*=false'
        os.environ['QT_ASSUME_STDERR_HAS_CONSOLE'] = '0'
        os.environ['PYTHONIOENCODING'] = 'utf-8'
        os.environ['PYTHONUNBUFFERED'] = '0'
        os.environ['PYTHONDONTWRITEBYTECODE'] = '1'

        # 禁用PowerShell相关环境变量
        os.environ['VIRTUAL_ENV_DISABLE_PROMPT'] = '1'
        os.environ['PYTHONSTARTUP'] = ''

        # 重定向标准输出到空设备
        devnull = open(os.devnull, 'w')
        sys.stderr = devnull
        sys.stdout = devnull
    except:
        pass

import enhanced_logger as logger

# 添加多进程支持（修复Spleeter多进程问题）
from multiprocessing import freeze_support

# 确保status_manager中的功能全局可用
from status_manager import update_feature_status, reset_feature_status, output_feature_summary

# 定义全局函数，以防任何模块找不到update_feature_status
def update_feature_status_global(feature_name, status="成功", details=""):
    """确保update_feature_status在全局范围内可用"""
    return update_feature_status(feature_name, status, details)

# 检查并添加 FFmpeg 到环境变量（如果需要）
def setup_ffmpeg_path() -> None:
    """智能设置FFmpeg路径，优先使用系统PATH中的FFmpeg"""
    # 首先检查系统PATH中是否已有FFmpeg
    try:
        result = subprocess.run(['ffmpeg', '-version'],
                              capture_output=True,
                              text=True,
                              timeout=5)
        if result.returncode == 0:
            logger.log_message("[系统] 使用系统PATH中的FFmpeg")
            return  # 系统PATH中已有FFmpeg，无需添加
    except (FileNotFoundError, subprocess.TimeoutExpired):
        pass

    # 如果系统PATH中没有FFmpeg，尝试添加本地FFmpeg路径
    possible_paths = [
        r"E:\ffmpeg-n7.1.1-20-g9373b442a6-win64-gpl-7.1\bin",
        r"C:\ffmpeg\bin",
        r"D:\ffmpeg\bin",
        os.path.join(os.path.dirname(os.path.abspath(__file__)), "ffmpeg", "bin")
    ]

    for ffmpeg_path in possible_paths:
        if os.path.exists(ffmpeg_path):
            ffmpeg_exe = os.path.join(ffmpeg_path, "ffmpeg.exe")
            if os.path.exists(ffmpeg_exe):
                # 只在当前进程中添加到PATH前面
                if 'PATH' in os.environ:
                    os.environ['PATH'] = ffmpeg_path + os.pathsep + os.environ['PATH']
                else:
                    os.environ['PATH'] = ffmpeg_path
                logger.log_message(f"[系统] 添加FFmpeg路径: {ffmpeg_path}")
                return

    # 如果都找不到，记录警告但不影响程序运行
    logger.log_message("[警告] 未找到FFmpeg，将尝试使用系统默认路径")

# 设置FFmpeg路径
setup_ffmpeg_path()

# 公共的FFmpeg进程清理函数
def kill_ffmpeg_processes(timeout: int = 5) -> bool:
    """清理所有FFmpeg进程

    Args:
        timeout: 超时时间（秒）

    Returns:
        bool: 清理是否成功
    """
    try:
        if sys.platform.startswith('win'):
            # 添加更详细的错误处理
            result = subprocess.run(['taskkill', '/f', '/im', 'ffmpeg.exe'],
                                  capture_output=True, timeout=timeout,
                                  creationflags=subprocess.CREATE_NO_WINDOW)
            return result.returncode == 0
    except subprocess.TimeoutExpired:
        logger.debug("FFmpeg进程清理超时")
    except FileNotFoundError:
        logger.debug("taskkill命令未找到，跳过FFmpeg清理")
    except Exception as e:
        logger.debug(f"FFmpeg进程清理异常: {e}")
    return False

# 从status_manager导入update_feature_status函数
# from status_manager import update_feature_status # This line is now redundant as update_feature_status is imported globally

# 紧急FFmpeg进程清理函数
def emergency_kill_ffmpeg() -> bool:
    """紧急清理所有FFmpeg进程，用于软件关闭时的快速清理

    Returns:
        bool: 清理是否成功
    """
    try:
        if sys.platform.startswith('win'):
            # 使用最短超时时间，立即强制终止
            subprocess.run(['taskkill', '/f', '/im', 'ffmpeg.exe'],
                         capture_output=True, timeout=1, check=False)
            return True
    except Exception:
        pass
    return False

# 使用Windows API强制清理进程
def windows_api_kill_ffmpeg() -> bool:
    """使用Windows API强制清理FFmpeg进程"""
    try:
        if not sys.platform.startswith('win'):
            return False

        import ctypes
        from ctypes import wintypes

        # Windows API常量
        PROCESS_TERMINATE = 0x0001
        TH32CS_SNAPPROCESS = 0x00000002

        # 定义结构体
        class PROCESSENTRY32(ctypes.Structure):
            _fields_ = [
                ("dwSize", wintypes.DWORD),
                ("cntUsage", wintypes.DWORD),
                ("th32ProcessID", wintypes.DWORD),
                ("th32DefaultHeapID", ctypes.POINTER(wintypes.ULONG)),
                ("th32ModuleID", wintypes.DWORD),
                ("cntThreads", wintypes.DWORD),
                ("th32ParentProcessID", wintypes.DWORD),
                ("pcPriClassBase", wintypes.LONG),
                ("dwFlags", wintypes.DWORD),
                ("szExeFile", wintypes.CHAR * 260)
            ]

        kernel32 = ctypes.windll.kernel32

        # 创建进程快照
        snapshot = kernel32.CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0)
        if snapshot == -1:
            return False

        process_entry = PROCESSENTRY32()
        process_entry.dwSize = ctypes.sizeof(PROCESSENTRY32)

        killed_count = 0

        # 遍历进程
        if kernel32.Process32First(snapshot, ctypes.byref(process_entry)):
            while True:
                exe_name = process_entry.szExeFile.decode('utf-8', errors='ignore')
                if exe_name.lower() == 'ffmpeg.exe':
                    # 找到FFmpeg进程，尝试终止
                    process_handle = kernel32.OpenProcess(PROCESS_TERMINATE, False, process_entry.th32ProcessID)
                    if process_handle:
                        if kernel32.TerminateProcess(process_handle, 1):
                            killed_count += 1
                            logger.log_message(f"[Windows API] 已终止FFmpeg进程 PID: {process_entry.th32ProcessID}")
                        kernel32.CloseHandle(process_handle)

                if not kernel32.Process32Next(snapshot, ctypes.byref(process_entry)):
                    break

        kernel32.CloseHandle(snapshot)

        if killed_count > 0:
            logger.log_message(f"[Windows API] 共终止 {killed_count} 个FFmpeg进程")
            return True
        else:
            logger.log_message("[Windows API] 没有发现FFmpeg进程")
            return True

    except Exception as e:
        logger.log_message(f"[Windows API] 清理失败: {e}")
        return False

# 超级静默清理函数（绝对无输出）
def ultra_silent_kill_processes() -> bool:
    """超级静默清理，使用最强的静默机制"""
    try:
        if sys.platform.startswith('win'):
            # 使用Python的os模块直接调用Windows API
            import ctypes
            from ctypes import wintypes

            try:
                # 获取所有进程快照
                kernel32 = ctypes.windll.kernel32
                TH32CS_SNAPPROCESS = 0x00000002

                # 创建进程快照
                snapshot = kernel32.CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0)
                if snapshot == -1:
                    return False

                # 定义进程结构
                class PROCESSENTRY32(ctypes.Structure):
                    _fields_ = [
                        ("dwSize", wintypes.DWORD),
                        ("cntUsage", wintypes.DWORD),
                        ("th32ProcessID", wintypes.DWORD),
                        ("th32DefaultHeapID", ctypes.POINTER(wintypes.ULONG)),
                        ("th32ModuleID", wintypes.DWORD),
                        ("cntThreads", wintypes.DWORD),
                        ("th32ParentProcessID", wintypes.DWORD),
                        ("pcPriClassBase", wintypes.LONG),
                        ("dwFlags", wintypes.DWORD),
                        ("szExeFile", wintypes.CHAR * 260)
                    ]

                pe32 = PROCESSENTRY32()
                pe32.dwSize = ctypes.sizeof(PROCESSENTRY32)

                # 遍历进程
                if kernel32.Process32First(snapshot, ctypes.byref(pe32)):
                    while True:
                        exe_name = pe32.szExeFile.decode('utf-8', errors='ignore').lower()

                        # 清理ffmpeg进程
                        if 'ffmpeg.exe' in exe_name:
                            try:
                                process_handle = kernel32.OpenProcess(0x0001, False, pe32.th32ProcessID)
                                if process_handle:
                                    kernel32.TerminateProcess(process_handle, 0)
                                    kernel32.CloseHandle(process_handle)
                            except:
                                pass

                        # 清理可能的孤儿Python进程（但排除当前进程）
                        elif ('python.exe' in exe_name or 'pythonw.exe' in exe_name) and pe32.th32ProcessID != os.getpid():
                            try:
                                # 检查是否是我们程序启动的子进程
                                if pe32.th32ParentProcessID == os.getpid():
                                    process_handle = kernel32.OpenProcess(0x0001, False, pe32.th32ProcessID)
                                    if process_handle:
                                        kernel32.TerminateProcess(process_handle, 0)
                                        kernel32.CloseHandle(process_handle)
                            except:
                                pass

                        if not kernel32.Process32Next(snapshot, ctypes.byref(pe32)):
                            break

                kernel32.CloseHandle(snapshot)
                return True

            except:
                # 如果Windows API失败，回退到静默subprocess
                try:
                    subprocess.run(['taskkill', '/f', '/im', 'ffmpeg.exe'],
                                 capture_output=True, timeout=1, check=False,
                                 creationflags=subprocess.CREATE_NO_WINDOW,
                                 stdin=subprocess.DEVNULL, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
                except:
                    pass
                return True

        return True
    except:
        return False

# 清理孤儿Python进程
def cleanup_orphan_python_processes() -> bool:
    """清理可能的孤儿Python进程"""
    try:
        if sys.platform.startswith('win'):
            # 使用taskkill清理可能的孤儿Python进程
            try:
                # 只清理pythonw.exe（后台Python进程）
                subprocess.run(['taskkill', '/f', '/im', 'pythonw.exe'],
                             capture_output=True, timeout=2, check=False,
                             creationflags=subprocess.CREATE_NO_WINDOW,
                             stdin=subprocess.DEVNULL, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            except:
                pass
        return True
    except:
        return False

# 清理过多的Runtime Broker进程
def cleanup_excessive_runtime_broker() -> bool:
    """清理过多的Runtime Broker进程"""
    try:
        if sys.platform.startswith('win'):
            # 检查Runtime Broker进程数量
            try:
                result = subprocess.run(['tasklist', '/fi', 'imagename eq RuntimeBroker.exe', '/fo', 'csv'],
                                     capture_output=True, text=True, timeout=5, check=False,
                                     creationflags=subprocess.CREATE_NO_WINDOW,
                                     stdin=subprocess.DEVNULL, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)

                if result.returncode == 0 and 'RuntimeBroker.exe' in result.stdout:
                    # 计算进程数量
                    lines = result.stdout.strip().split('\n')
                    process_count = len([line for line in lines if 'RuntimeBroker.exe' in line])

                    # 如果Runtime Broker进程过多（超过5个），尝试清理一些
                    if process_count > 5:
                        logger.log_message(f"[系统清理] 发现 {process_count} 个Runtime Broker进程，尝试优化...")

                        # 不直接杀死Runtime Broker（系统进程），而是清理可能导致它们的应用
                        # 这里只记录，让系统自然清理
                        logger.log_message("[系统清理] Runtime Broker进程较多，建议重启系统以优化性能")
                        return True

            except:
                pass
        return True
    except:
        return False

# 静默清理函数（完全无输出）
def silent_kill_all_processes() -> bool:
    """完全静默清理所有相关进程，不产生任何输出或错误信息"""
    # 先清理FFmpeg进程
    ultra_silent_kill_processes()
    # 再清理可能的孤儿Python进程
    cleanup_orphan_python_processes()
    # 检查Runtime Broker进程
    cleanup_excessive_runtime_broker()
    return True

# 清理所有相关进程函数
def emergency_kill_all_processes() -> bool:
    """紧急清理所有软件相关进程，包括FFmpeg和音频分离工具

    Returns:
        bool: 清理是否成功
    """
    success = True
    try:
        if sys.platform.startswith('win'):
            logger.log_message("[紧急清理] 开始强制清理所有相关进程...")

            # 方法1：使用taskkill清理FFmpeg进程（立即强制终止）
            try:
                result1 = subprocess.run(['taskkill', '/f', '/im', 'ffmpeg.exe'],
                                       capture_output=True, timeout=1, check=False,
                                       creationflags=subprocess.CREATE_NO_WINDOW)
                logger.log_message(f"[紧急清理] taskkill ffmpeg 返回码: {result1.returncode}")
            except FileNotFoundError:
                logger.log_message("[紧急清理] taskkill命令未找到，跳过FFmpeg清理")
            except Exception as e:
                logger.log_message(f"[紧急清理] taskkill执行异常: {e}")

            # 方法2：使用wmic作为备用清理方法
            try:
                result2 = subprocess.run('wmic process where name="ffmpeg.exe" delete',
                                       shell=True, capture_output=True, timeout=3, check=False,
                                       creationflags=subprocess.CREATE_NO_WINDOW)
                logger.log_message(f"[紧急清理] wmic清理 返回码: {result2.returncode}")
            except FileNotFoundError:
                logger.log_message("[紧急清理] wmic命令未找到，跳过wmic清理")
            except Exception as e:
                logger.log_message(f"[紧急清理] wmic清理失败: {e}")

            # 方法3：验证清理结果
            try:
                verify_result = subprocess.run(['tasklist', '/fi', 'imagename eq ffmpeg.exe'],
                                             capture_output=True, text=True, timeout=2, check=False,
                                             creationflags=subprocess.CREATE_NO_WINDOW)
                if 'ffmpeg.exe' in verify_result.stdout:
                    logger.log_message("[紧急清理] ⚠️ 警告：仍有FFmpeg进程残留")
                    success = False
                else:
                    logger.log_message("[紧急清理] ✅ 确认：所有FFmpeg进程已清理")
            except FileNotFoundError:
                logger.log_message("[紧急清理] tasklist命令未找到，跳过验证")
            except Exception as e:
                logger.log_message(f"[紧急清理] 验证清理结果异常: {e}")

            # 如果常规方法失败，使用Windows API强制清理
            if not success:
                logger.log_message("[紧急清理] 常规方法失败，使用Windows API...")
                success = windows_api_kill_ffmpeg()

            # 清理可能的音频分离Python进程（Spleeter/Demucs）
            try:
                subprocess.run(['taskkill', '/f', '/fi', 'WINDOWTITLE eq *spleeter*'],
                             capture_output=True, timeout=1, check=False,
                             creationflags=subprocess.CREATE_NO_WINDOW)
                subprocess.run(['taskkill', '/f', '/fi', 'WINDOWTITLE eq *demucs*'],
                             capture_output=True, timeout=1, check=False,
                             creationflags=subprocess.CREATE_NO_WINDOW)
            except Exception as e:
                logger.log_message(f"[紧急清理] 音频分离进程清理异常: {e}")

            return success
    except Exception as e:
        logger.log_message(f"[紧急清理] 异常: {e}")
        success = False
    return success

# 启动时清理可能残留的FFmpeg进程
def cleanup_residual_ffmpeg() -> None:
    """启动时清理可能残留的FFmpeg进程"""
    try:
        if sys.platform.startswith('win'):
            logger.log_message("[启动] 🧹 检查并清理启动前残留的FFmpeg进程...")
            result = subprocess.run(['tasklist', '/fi', 'imagename eq ffmpeg.exe'],
                                  capture_output=True, text=True, timeout=5)
            if 'ffmpeg.exe' in result.stdout:
                logger.log_message("[启动] 🎯 发现残留的FFmpeg进程，正在清理...")
                if kill_ffmpeg_processes(timeout=10):
                    logger.log_message("[启动] ✅ 启动前FFmpeg进程清理完成")
                else:
                    logger.log_message("[启动] ⚠️ 启动前FFmpeg进程清理失败")
            else:
                logger.log_message("[启动] ✅ 没有发现残留的FFmpeg进程")
    except Exception as e:
        logger.log_message(f"[启动] ⚠️ 启动前FFmpeg清理失败: {e}")

# 执行启动时清理
cleanup_residual_ffmpeg()

# 设置信号处理器，处理强制终止情况
def setup_signal_handlers() -> None:
    """设置信号处理器，确保程序被强制终止时也能清理FFmpeg进程"""
    import signal

    def signal_handler(signum, _=None):
        logger.log_message(f"[信号] 🚨 接收到信号 {signum}，正在清理FFmpeg进程...")
        if kill_ffmpeg_processes():
            logger.log_message("[信号] ✅ 信号处理器已清理FFmpeg进程")
        else:
            logger.log_message("[信号] ❌ 信号处理器清理失败")

        # 退出程序
        sys.exit(0)

    # 注册信号处理器
    try:
        signal.signal(signal.SIGINT, signal_handler)   # Ctrl+C
        signal.signal(signal.SIGTERM, signal_handler)  # 终止信号
        if hasattr(signal, 'SIGBREAK'):
            signal.signal(signal.SIGBREAK, signal_handler)  # Windows Ctrl+Break
        logger.log_message("[启动] 🛡️ 信号处理器已设置")
    except Exception as e:
        logger.log_message(f"[启动] ⚠️ 信号处理器设置失败: {e}")

# 设置信号处理器
setup_signal_handlers()

# 创建FFmpeg进程监控线程
import threading
import time

class FFmpegMonitor:
    """FFmpeg进程监控器"""
    def __init__(self):
        self.running = True
        self.monitor_thread = None

    def start_monitoring(self):
        """启动监控线程"""
        if self.monitor_thread is None or not self.monitor_thread.is_alive():
            self.running = True
            self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self.monitor_thread.start()
            logger.log_message("[监控器] 🔍 FFmpeg进程监控器已启动")

    def stop_monitoring(self):
        """停止监控"""
        self.running = False
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=1)
            logger.log_message("[监控器] 🛑 FFmpeg进程监控器已停止")

    def _monitor_loop(self):
        """监控循环"""
        last_cleanup_time = 0
        while self.running:
            try:
                current_time = time.time()

                # 每30秒检查一次是否有孤立的FFmpeg进程
                if current_time - last_cleanup_time > 30:
                    self._check_orphaned_ffmpeg()
                    last_cleanup_time = current_time

                time.sleep(5)  # 每5秒检查一次监控状态

            except Exception as e:
                logger.log_message(f"[监控器] 监控异常: {e}")
                time.sleep(10)

    def _check_orphaned_ffmpeg(self):
        """检查孤立的FFmpeg进程"""
        try:
            if sys.platform.startswith('win'):
                # 检查FFmpeg进程，使用完整路径避免找不到文件的问题
                result = subprocess.run(['C:\\Windows\\System32\\tasklist.exe', '/fi', 'imagename eq ffmpeg.exe'],
                                      capture_output=True, text=True, timeout=5, check=False)

                if result.returncode == 0 and 'ffmpeg.exe' in result.stdout:
                    # 发现FFmpeg进程，检查是否为孤立进程
                    lines = result.stdout.strip().split('\n')
                    if len(lines) > 1:
                        process_count = len(lines) - 1

                        # 只有在进程数量异常多时才认为是孤立进程
                        # 正常情况下，同时运行2-3个FFmpeg进程是正常的
                        if process_count > 5:  # 超过5个进程才认为异常
                            logger.log_message(f"[监控器] 发现异常数量的FFmpeg进程({process_count}个)，执行清理")
                            emergency_kill_all_processes()
                        else:
                            # 正常数量的进程，不清理，只记录
                            logger.log_message(f"[监控器] 发现 {process_count} 个FFmpeg进程，数量正常，继续监控")

        except FileNotFoundError:
            # tasklist命令不存在，跳过监控
            pass
        except Exception as e:
            # 其他异常才记录日志
            if "系统找不到指定的文件" not in str(e):
                logger.log_message(f"[监控器] 孤立进程检查异常: {e}")

# 创建全局监控器实例
ffmpeg_monitor = FFmpegMonitor()

# 启动FFmpeg清理守护进程
def start_ffmpeg_killer_daemon():
    """启动FFmpeg清理守护进程"""
    # 临时禁用守护进程，避免误杀正在工作的FFmpeg进程
    logger.log_message("[启动] ⚠️ FFmpeg清理守护进程已临时禁用，避免误杀正在工作的进程")
    return

    try:
        import os
        script_path = os.path.join(os.path.dirname(__file__), 'ffmpeg_killer.py')
        if os.path.exists(script_path):
            # 启动守护进程
            subprocess.Popen([sys.executable, script_path, '--daemon'],
                           creationflags=subprocess.CREATE_NO_WINDOW if sys.platform.startswith('win') else 0)
            logger.log_message("[启动] 🛡️ FFmpeg清理守护进程已启动")
        else:
            logger.log_message("[启动] ⚠️ FFmpeg清理脚本不存在")
    except Exception as e:
        logger.log_message(f"[启动] ❌ 启动FFmpeg清理守护进程失败: {e}")

# 启动守护进程和监控器
start_ffmpeg_killer_daemon()
ffmpeg_monitor.start_monitoring()

# 设置程序退出时的清理钩子
import atexit
def final_cleanup():
    """程序退出前的最终清理工作 - 修改版，使用同步方式清理FFmpeg进程"""
    try:
        logger.log_message("[退出] 🧹 执行最终清理...")
        
        # 0. 停止FFmpeg监控器
        try:
            ffmpeg_monitor.stop_monitoring()
            logger.log_message("[退出] ✅ FFmpeg监控器已停止")
        except Exception as e:
            logger.log_message(f"[退出] ⚠️ 停止FFmpeg监控器失败: {e}")
        
        # 1. 同步清理所有FFmpeg进程
        if sys.platform.startswith('win'):
            # 使用同步方法清理FFmpeg进程
            try:
                # 方法1: 同步taskkill
                subprocess.run(['taskkill', '/f', '/im', 'ffmpeg.exe'], 
                              timeout=3, check=False,
                              creationflags=subprocess.CREATE_NO_WINDOW)
                
                # 方法2: 使用Windows API方法强制清理
                windows_api_kill_ffmpeg()
                
                # 方法3: 验证清理结果
                verify_result = subprocess.run(['tasklist', '/fi', 'imagename eq ffmpeg.exe'],
                                             capture_output=True, text=True, timeout=2, check=False,
                                             creationflags=subprocess.CREATE_NO_WINDOW)
                
                if 'ffmpeg.exe' in verify_result.stdout:
                    logger.log_message("[退出] ⚠️ 警告：仍有FFmpeg进程残留，尝试强制清理...")
                    # 最后尝试使用wmic强制清理
                    subprocess.run('wmic process where name="ffmpeg.exe" delete',
                                 shell=True, capture_output=True, timeout=3, check=False,
                                 creationflags=subprocess.CREATE_NO_WINDOW)
                else:
                    logger.log_message("[退出] ✅ FFmpeg进程清理完成")
                
                logger.log_message("[退出] ✅ FFmpeg进程清理命令已完成")
            except Exception as e:
                logger.log_message(f"[退出] ⚠️ FFmpeg清理异常: {e}")
        
        # 2. 同步清理临时文件
        try:
            # 清理临时音频文件
            temp_audio_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "temp_audio")
            if os.path.exists(temp_audio_dir):
                for file in os.listdir(temp_audio_dir):
                    if file.endswith(".wav") or file.endswith(".mp3"):
                        try:
                            os.remove(os.path.join(temp_audio_dir, file))
                        except:
                            pass
            logger.log_message("[退出] ✅ 临时音频文件清理完成")
        except Exception as e:
            logger.log_message(f"[退出] ⚠️ 临时文件清理异常: {e}")
        
        logger.log_message("[退出] 🏁 最终清理完成")
    except Exception as e:
        logger.log_message(f"[退出] ❌ 最终清理失败: {e}")

atexit.register(final_cleanup)

# 设置默认编码为UTF-8
if sys.platform == 'win32':
    # Windows下确保使用UTF-8编码
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
    sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')

    # 全局设置subprocess隐藏命令行窗口（修复版本）
    import subprocess
    if not hasattr(subprocess, '_original_run'):
        # 只修改 subprocess.run，不修改 subprocess.Popen 类
        # 这样避免影响 asyncio 等模块对 Popen 类的继承
        subprocess._original_run = subprocess.run
        def _hidden_run(*args, **kwargs):
            if 'creationflags' not in kwargs:
                kwargs['creationflags'] = subprocess.CREATE_NO_WINDOW
            return subprocess._original_run(*args, **kwargs)
        subprocess.run = _hidden_run

# 2023-10-08: 修复了所有subprocess.Popen调用中可能出现的GBK编码错误
# 修复方法：1. 对于打开资源管理器的调用，添加shell=True参数
#          2. 对于其他调用，确保使用encoding='utf-8', errors='replace'参数

# 导入自定义错误处理模块并安装全局错误处理器
from error_handler import install_global_handler
install_global_handler()

# 设置MoviePy等临时文件目录到项目下的temp文件夹
os.environ['TMPDIR'] = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'temp')
os.environ['TEMP'] = os.environ['TMPDIR']
os.environ['TMP'] = os.environ['TMPDIR']

# 导入配置管理器并加载配置
import config_manager
config_manager.load_config()

# 在导入UI相关模块前，先初始化QApplication
from PyQt5.QtWidgets import QApplication

# 创建QApplication实例（如果不存在）或获取已有实例
# 使用类型注解来避免 Pyright 的检查问题
app: Any = QApplication.instance() or QApplication(sys.argv)
logger.debug(f"QApplication实例: {'已存在' if QApplication.instance() != app else '新创建'}")

from ui_mainwindow import VideoPublisher

if __name__ == "__main__":
    # 添加多进程支持（修复Spleeter多进程问题）
    freeze_support()

    # 设置完全静默模式
    import sys
    import os

    # 启动前清理可能的残留进程
    try:
        ultra_silent_kill_processes()
        cleanup_orphan_python_processes()
    except:
        pass

    # 完全禁用所有输出（Windows）
    if sys.platform.startswith('win'):
        try:
            # 重定向所有标准流到空设备
            devnull = open(os.devnull, 'w')
            sys.stdout = devnull
            sys.stderr = devnull

            # 禁用Qt的调试输出
            os.environ['QT_LOGGING_RULES'] = '*=false'
            os.environ['QT_ASSUME_STDERR_HAS_CONSOLE'] = '0'

        except:
            pass

    # 使用已创建的QApplication实例
    window = VideoPublisher()
    window.show()

    # 添加应用退出前的处理 - 使用Windows API
    def on_app_exit() -> None:
        try:
            logger.log_message("[应用退出] 开始同步清理FFmpeg和Python进程...")
            
            # 清理所有已跟踪的进程(如果window对象存在terminate_all_processes方法)
            try:
                if hasattr(window, 'terminate_all_processes'):
                    logger.log_message("[应用退出] 调用窗口的进程终止方法...")
                    window.terminate_all_processes()
            except Exception as e:
                logger.log_message(f"[应用退出] 调用终止方法异常: {e}")
            
            # 使用同步方式清理FFmpeg进程
            try:
                # 方法1: 使用taskkill
                subprocess.run(['taskkill', '/f', '/im', 'ffmpeg.exe'], 
                              timeout=3, check=False,
                              creationflags=subprocess.CREATE_NO_WINDOW)
                
                # 方法2: 使用Windows API进行强制清理
                windows_api_kill_ffmpeg()
                
                logger.log_message("[应用退出] FFmpeg进程清理完成")
            except Exception as e:
                logger.log_message(f"[应用退出] FFmpeg清理异常: {e}")
                
            # 清理Python子进程（单视频模式可能遗留的子进程）
            try:
                current_pid = os.getpid()
                logger.log_message(f"[应用退出] 当前主进程ID: {current_pid}")
                
                # 查找Python进程
                result = subprocess.run(['tasklist', '/fi', 'imagename eq python.exe', '/fo', 'csv'],
                                      capture_output=True, text=True, timeout=2, check=False)
                
                if 'python.exe' in result.stdout:
                    # 检查是否有子进程
                    lines = result.stdout.strip().split('\n')
                    for line in lines[1:]:  # 跳过标题行
                        parts = line.split(',')
                        if len(parts) >= 2:
                            process_name = parts[0].strip('"')
                            pid = parts[1].strip('"')
                            pid = int(pid)
                            
                            # 不终止当前进程
                            if pid != current_pid:
                                logger.log_message(f"[应用退出] 终止Python进程: {pid}")
                                try:
                                    subprocess.run(['taskkill', '/f', '/pid', str(pid)],
                                                 capture_output=True, timeout=1, check=False)
                                except Exception as e:
                                    logger.log_message(f"[应用退出] 终止Python进程失败: {e}")
            except Exception as e:
                logger.log_message(f"[应用退出] Python进程清理异常: {e}")

            # 静默保存配置
            try:
                if hasattr(config_manager, 'save_config'):
                    config_manager.save_config()
                    logger.log_message("[应用退出] 配置保存完成")
            except Exception as e:
                logger.log_message(f"[应用退出] 配置保存异常: {e}")
        except Exception as e:
            logger.log_message(f"[应用退出] 处理异常: {e}")

    app.aboutToQuit.connect(on_app_exit)

    # 重写窗口关闭事件，确保用户点击X按钮时也能清理进程
    original_close_event = window.closeEvent if hasattr(window, 'closeEvent') else None

    def enhanced_close_event(event):
        try:
            logger.log_message("[关闭] 开始同步清理FFmpeg进程...")
            # 使用同步方式清理FFmpeg进程
            try:
                # 方法1: 使用taskkill
                subprocess.run(['taskkill', '/f', '/im', 'ffmpeg.exe'], 
                              timeout=3, check=False,
                              creationflags=subprocess.CREATE_NO_WINDOW)
                
                # 方法2: 使用Windows API进行强制清理
                windows_api_kill_ffmpeg()
                
                logger.log_message("[关闭] FFmpeg进程清理完成")
            except Exception as e:
                logger.log_message(f"[关闭] FFmpeg清理异常: {e}")

            # 调用原始关闭事件处理
            if original_close_event:
                original_close_event(event)
            else:
                event.accept()
        except Exception as e:
            # 确保窗口能够关闭，忽略所有异常
            logger.log_message(f"[关闭] 处理异常: {e}")
            event.accept()

    window.closeEvent = enhanced_close_event

    # 使用类型注解避免 Pyright 检查问题
    exit_code: int = app.exec_()
    sys.exit(exit_code)
    