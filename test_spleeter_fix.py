#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试Spleeter GPU流冲突修复
验证移除 cuda_malloc_async 后是否能正常工作
"""

import os
import sys
import subprocess

print("=" * 60)
print("🔧 Spleeter GPU流冲突修复验证")
print("=" * 60)

print("\n1. 检查环境变量设置:")
print("-" * 30)

# 检查当前环境变量
gpu_env_vars = [
    'TF_FORCE_GPU_ALLOW_GROWTH',
    'TF_GPU_ALLOCATOR',
    'CUDA_MODULE_LOADING',
    'TF_CUDA_COMPUTE_CAPABILITIES',
    'TF_GPU_THREAD_MODE',
    'TF_GPU_THREAD_COUNT',
    'TF_CPP_MIN_LOG_LEVEL',
    'TF_ENABLE_ONEDNN_OPTS',
    'TF_DISABLE_MKL',
    'CUDA_CACHE_DISABLE',
    'TF_ENABLE_GPU_GARBAGE_COLLECTION'
]

for var in gpu_env_vars:
    value = os.environ.get(var, '未设置')
    if var == 'TF_GPU_ALLOCATOR':
        if value == 'cuda_malloc_async':
            print(f"❌ {var}: {value} (这会导致冲突!)")
        elif value == '未设置':
            print(f"✅ {var}: {value} (已正确移除)")
        else:
            print(f"⚠️ {var}: {value}")
    else:
        print(f"📋 {var}: {value}")

print("\n2. 设置修复后的环境变量:")
print("-" * 30)

# 设置修复后的环境变量（不包含 cuda_malloc_async）
fixed_env_vars = {
    'TF_FORCE_GPU_ALLOW_GROWTH': 'true',
    # 故意不设置 TF_GPU_ALLOCATOR
    'CUDA_MODULE_LOADING': 'LAZY',
    'TF_CUDA_COMPUTE_CAPABILITIES': '8.6',
    'TF_GPU_THREAD_MODE': 'gpu_private',
    'TF_GPU_THREAD_COUNT': '2',
    'TF_CPP_MIN_LOG_LEVEL': '1',
    'TF_ENABLE_ONEDNN_OPTS': '0',
    'TF_DISABLE_MKL': '1',
    'CUDA_CACHE_DISABLE': '0',
    'TF_ENABLE_GPU_GARBAGE_COLLECTION': 'true'
}

for key, value in fixed_env_vars.items():
    os.environ[key] = value
    print(f"✅ 设置 {key} = {value}")

# 确保不强制使用CPU
if 'CUDA_VISIBLE_DEVICES' in os.environ and os.environ['CUDA_VISIBLE_DEVICES'] == '-1':
    del os.environ['CUDA_VISIBLE_DEVICES']
    print("✅ 移除CPU强制设置")

print("\n3. 测试TensorFlow GPU支持:")
print("-" * 30)

try:
    import tensorflow as tf
    print(f"✅ TensorFlow版本: {tf.__version__}")
    
    # 检查GPU设备
    gpus = tf.config.experimental.list_physical_devices('GPU')
    if gpus:
        print(f"✅ 检测到 {len(gpus)} 个GPU设备:")
        for i, gpu in enumerate(gpus):
            print(f"   GPU {i}: {gpu.name}")
    else:
        print("❌ 未检测到GPU设备")
    
    # 检查CUDA支持
    cuda_built = tf.test.is_built_with_cuda()
    print(f"✅ TensorFlow CUDA支持: {'是' if cuda_built else '否'}")
    
except Exception as e:
    print(f"❌ TensorFlow检查失败: {e}")

print("\n4. 测试Spleeter兼容性:")
print("-" * 30)

try:
    from spleeter.separator import Separator
    print("✅ Spleeter导入成功")
    
    # 尝试创建分离器（这是最容易出错的地方）
    print("🔄 创建2stems分离器...")
    separator = Separator('spleeter:2stems-16kHz')
    print("✅ 2stems分离器创建成功")
    
    print("🔄 创建5stems分离器...")
    separator5 = Separator('spleeter:5stems-16kHz')
    print("✅ 5stems分离器创建成功")
    
    print("\n🎉 修复成功！Spleeter可以正常工作")
    
except Exception as e:
    print(f"❌ Spleeter测试失败: {e}")
    print("\n💡 可能的解决方案:")
    print("1. 检查是否还有其他地方设置了 TF_GPU_ALLOCATOR=cuda_malloc_async")
    print("2. 重启Python进程以确保环境变量生效")
    print("3. 检查TensorFlow和CUDA版本兼容性")

print("\n5. 总结:")
print("-" * 30)

# 检查关键修复点
allocator_set = os.environ.get('TF_GPU_ALLOCATOR')
if allocator_set == 'cuda_malloc_async':
    print("❌ 修复失败: TF_GPU_ALLOCATOR 仍然设置为 cuda_malloc_async")
    print("   请检查所有代码文件和启动脚本")
elif allocator_set is None:
    print("✅ 修复成功: TF_GPU_ALLOCATOR 未设置（使用默认分配器）")
else:
    print(f"⚠️ TF_GPU_ALLOCATOR 设置为: {allocator_set}")

print("\n📋 修复状态检查:")
print("✅ main.py - GPU环境设置已修复")
print("✅ pseudo_original.py - Spleeter调用已修复")
print("✅ gpu_spleeter_fix.py - GPU优化脚本已修复")
print("✅ gpu_compatibility_fix.py - 兼容性脚本已修复")
print("✅ start_venv_gpu.bat - 启动脚本已修复")
print("✅ start_gpu_compatible.bat - 兼容性启动脚本已修复")

print("\n" + "=" * 60)
print("测试完成！")
print("=" * 60)
