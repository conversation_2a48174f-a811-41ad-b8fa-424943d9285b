@echo off
echo 正在启动视频处理程序（GPU兼容性模式）...

REM 设置GPU兼容性环境变量
set TF_FORCE_GPU_ALLOW_GROWTH=true
REM 移除 cuda_malloc_async 避免与Spleeter的GPU流管理冲突
REM set TF_GPU_ALLOCATOR=cuda_malloc_async
set CUDA_MODULE_LOADING=LAZY
set TF_CUDA_COMPUTE_CAPABILITIES=8.6
set TF_GPU_THREAD_MODE=gpu_private
set TF_GPU_THREAD_COUNT=2
set TF_CPP_MIN_LOG_LEVEL=1
set TF_ENABLE_ONEDNN_OPTS=0
set TF_DISABLE_MKL=1

echo GPU兼容性环境变量设置完成
echo 启动程序...
python main.py
pause
