#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
GPU兼容性修复工具 - 解决CUDA版本不匹配问题
专门针对CUDA 12.x与TensorFlow 2.9.x的兼容性问题
"""

import os
import sys
import subprocess

def set_gpu_compatibility_env():
    """设置GPU兼容性环境变量"""
    print("🔧 设置GPU兼容性环境变量...")
    
    # 关键的兼容性设置
    env_vars = {
        # 强制TensorFlow使用兼容模式
        'TF_FORCE_GPU_ALLOW_GROWTH': 'true',
        # 移除 cuda_malloc_async 避免与Spleeter的GPU流管理冲突
        # 'TF_GPU_ALLOCATOR': 'cuda_malloc_async',

        # CUDA兼容性设置
        'CUDA_MODULE_LOADING': 'LAZY',
        'TF_CUDA_COMPUTE_CAPABILITIES': '8.6',  # RTX 3060的计算能力

        # 内存管理优化
        'TF_GPU_THREAD_MODE': 'gpu_private',
        'TF_GPU_THREAD_COUNT': '2',

        # 减少日志输出
        'TF_CPP_MIN_LOG_LEVEL': '1',

        # 兼容性模式
        'TF_ENABLE_ONEDNN_OPTS': '0',
        'TF_DISABLE_MKL': '1'
    }
    
    for key, value in env_vars.items():
        os.environ[key] = value
        print(f"   ✅ {key} = {value}")
    
    print("✅ GPU兼容性环境变量设置完成")

def configure_tensorflow_gpu():
    """配置TensorFlow GPU设置"""
    print("\n🔧 配置TensorFlow GPU设置...")
    
    try:
        import tensorflow as tf
        
        # 获取GPU设备
        gpus = tf.config.experimental.list_physical_devices('GPU')
        
        if gpus:
            print(f"✅ 检测到 {len(gpus)} 个GPU设备")
            
            for gpu in gpus:
                # 启用内存增长
                tf.config.experimental.set_memory_growth(gpu, True)
                print(f"   ✅ 为 {gpu.name} 启用内存增长")
                
                # 设置内存限制（避免OOM）
                try:
                    tf.config.experimental.set_memory_limit(gpu, 8192)  # 8GB限制
                    print(f"   ✅ 为 {gpu.name} 设置8GB内存限制")
                except:
                    print(f"   ⚠️ 无法为 {gpu.name} 设置内存限制")
            
            # 设置软设备放置
            tf.config.set_soft_device_placement(True)
            print("   ✅ 启用软设备放置")
            
            # 禁用JIT编译（避免兼容性问题）
            tf.config.optimizer.set_jit(False)
            print("   ✅ 禁用JIT编译")
            
            return True
        else:
            print("❌ 未检测到GPU设备")
            return False
            
    except Exception as e:
        print(f"❌ TensorFlow GPU配置失败: {e}")
        return False

def test_spleeter_gpu_compatibility():
    """测试Spleeter GPU兼容性"""
    print("\n🧪 测试Spleeter GPU兼容性...")
    
    try:
        # 应用所有修复
        set_gpu_compatibility_env()
        configure_tensorflow_gpu()
        
        # 重新导入以应用设置
        if 'spleeter.separator' in sys.modules:
            del sys.modules['spleeter.separator']
        if 'tensorflow' in sys.modules:
            # 不能删除tensorflow模块，但可以重新配置
            pass
        
        print("   正在导入Spleeter...")
        from spleeter.separator import Separator
        
        print("   正在加载2stems模型...")
        separator = Separator('spleeter:2stems-16kHz')
        
        print("✅ Spleeter GPU兼容性测试成功！")
        return True
        
    except Exception as e:
        error_str = str(e).lower()
        print(f"❌ Spleeter GPU测试失败: {e}")
        
        # 提供具体的解决建议
        if "unimplementederror" in error_str:
            print("\n💡 UnimplementedError解决方案:")
            print("   这是CUDA版本不匹配的典型错误")
            print("   1. 您的CUDA 12.9与TensorFlow 2.9.3不完全兼容")
            print("   2. 建议降级到CUDA 11.2或升级TensorFlow到2.12+")
            print("   3. 或者使用我们的兼容性补丁")
        elif "could not load dynamic library" in error_str:
            print("\n💡 动态库加载失败解决方案:")
            print("   1. 缺少cuDNN或CUDA库文件")
            print("   2. 检查CUDA安装路径")
            print("   3. 重新安装CUDA Toolkit")
        
        return False

def create_gpu_startup_script():
    """创建GPU优化启动脚本"""
    print("\n📝 创建GPU优化启动脚本...")
    
    script_content = '''@echo off
echo 正在启动视频处理程序（GPU兼容性模式）...

REM 设置GPU兼容性环境变量
set TF_FORCE_GPU_ALLOW_GROWTH=true
REM 移除 cuda_malloc_async 避免与Spleeter的GPU流管理冲突
REM set TF_GPU_ALLOCATOR=cuda_malloc_async
set CUDA_MODULE_LOADING=LAZY
set TF_CUDA_COMPUTE_CAPABILITIES=8.6
set TF_GPU_THREAD_MODE=gpu_private
set TF_GPU_THREAD_COUNT=2
set TF_CPP_MIN_LOG_LEVEL=1
set TF_ENABLE_ONEDNN_OPTS=0
set TF_DISABLE_MKL=1

echo GPU兼容性环境变量设置完成
echo 启动程序...
python main.py
pause
'''
    
    try:
        with open('start_gpu_compatible.bat', 'w', encoding='utf-8') as f:
            f.write(script_content)
        print("✅ 创建了启动脚本: start_gpu_compatible.bat")
        return True
    except Exception as e:
        print(f"❌ 创建启动脚本失败: {e}")
        return False

def apply_spleeter_patch():
    """应用Spleeter兼容性补丁"""
    print("\n🔧 应用Spleeter兼容性补丁...")
    
    # 创建补丁代码
    patch_code = '''
import os
import tensorflow as tf

# 在导入Spleeter之前应用补丁
def apply_gpu_compatibility_patch():
    """应用GPU兼容性补丁"""
    
    # 设置环境变量
    os.environ['TF_FORCE_GPU_ALLOW_GROWTH'] = 'true'
    os.environ['TF_GPU_ALLOCATOR'] = 'cuda_malloc_async'
    os.environ['CUDA_MODULE_LOADING'] = 'LAZY'
    os.environ['TF_CUDA_COMPUTE_CAPABILITIES'] = '8.6'
    
    # 配置GPU
    gpus = tf.config.experimental.list_physical_devices('GPU')
    if gpus:
        for gpu in gpus:
            tf.config.experimental.set_memory_growth(gpu, True)
            try:
                tf.config.experimental.set_memory_limit(gpu, 8192)
            except:
                pass
        tf.config.set_soft_device_placement(True)
        tf.config.optimizer.set_jit(False)

# 自动应用补丁
apply_gpu_compatibility_patch()
'''
    
    try:
        with open('spleeter_gpu_patch.py', 'w', encoding='utf-8') as f:
            f.write(patch_code)
        print("✅ 创建了兼容性补丁: spleeter_gpu_patch.py")
        print("   在使用Spleeter前导入此模块: import spleeter_gpu_patch")
        return True
    except Exception as e:
        print(f"❌ 创建补丁失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🎯 GPU兼容性修复工具 (CUDA 12.x + TensorFlow 2.9.x)")
    print("=" * 60)
    
    # 应用环境变量修复
    set_gpu_compatibility_env()
    
    # 测试GPU兼容性
    gpu_ok = test_spleeter_gpu_compatibility()
    
    # 创建启动脚本
    create_gpu_startup_script()
    
    # 创建补丁文件
    apply_spleeter_patch()
    
    print("\n" + "=" * 60)
    if gpu_ok:
        print("🎉 GPU兼容性修复成功！")
        print("\n📋 使用方法:")
        print("1. 使用 start_gpu_compatible.bat 启动程序（推荐）")
        print("2. 或在代码中导入: import spleeter_gpu_patch")
        print("3. GPU将使用兼容性模式运行Spleeter")
    else:
        print("⚠️ GPU兼容性仍有问题，但已创建修复工具")
        print("\n📋 备选方案:")
        print("1. 尝试使用 start_gpu_compatible.bat")
        print("2. 如果仍然失败，考虑使用CPU模式")
        print("3. 或者升级到兼容的TensorFlow版本")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
