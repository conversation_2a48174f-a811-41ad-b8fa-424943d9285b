
import os
import tensorflow as tf

# 在导入Spleeter之前应用补丁
def apply_gpu_compatibility_patch():
    """应用GPU兼容性补丁"""
    
    # 设置环境变量
    os.environ['TF_FORCE_GPU_ALLOW_GROWTH'] = 'true'
    os.environ['TF_GPU_ALLOCATOR'] = 'cuda_malloc_async'
    os.environ['CUDA_MODULE_LOADING'] = 'LAZY'
    os.environ['TF_CUDA_COMPUTE_CAPABILITIES'] = '8.6'
    
    # 配置GPU
    gpus = tf.config.experimental.list_physical_devices('GPU')
    if gpus:
        for gpu in gpus:
            tf.config.experimental.set_memory_growth(gpu, True)
            try:
                tf.config.experimental.set_memory_limit(gpu, 8192)
            except:
                pass
        tf.config.set_soft_device_placement(True)
        tf.config.optimizer.set_jit(False)

# 自动应用补丁
apply_gpu_compatibility_patch()
